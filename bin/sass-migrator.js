#!/usr/bin/env node

const { Command } = require('commander');
const path = require('path');
const chalk = require('chalk');
const SassMigrator = require('../src/sassMigrator');
const EnhancedSassMigrator = require('../src/sass/enhancedSassMigrator');

const program = new Command();

program
  .name('sass-migrator')
  .description('Vue 项目 Sass 语法迁移工具 (@import 到 @use)')
  .version('1.0.0');

program
  .argument('<project-path>', '项目路径')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--no-backup', '不创建备份文件')
  .option('--verbose', '显示详细输出')
  .option('--include <patterns...>', 'Sass 文件匹配模式', ['**/*.scss', '**/*.sass'])
  .option('--exclude <patterns...>', '排除的文件模式', ['node_modules/**', 'dist/**', 'build/**'])
  .option('--enhanced', '使用增强版迁移器（推荐）')
  .option('--no-architecture-refactor', '禁用架构重构')
  .option('--no-element-plus', '禁用 Element Plus 迁移')
  .option('--no-vite-optimization', '禁用 Vite 配置优化')
  .option('--no-error-diagnostic', '禁用错误诊断')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .action(async (projectPath, options) => {
    try {
      console.log(chalk.bold.blue('\n🎨 Sass 语法迁移工具'));
      console.log(chalk.gray(`项目路径: ${path.resolve(projectPath)}`));

      if (options.dryRun) {
        console.log(chalk.yellow('🔍 运行在预览模式，不会修改文件'));
      }

      console.log('');

      // 选择迁移器
      let migrator;
      let result;

      if (options.enhanced) {
        console.log(chalk.blue('🚀 使用增强版迁移器'));

        migrator = new EnhancedSassMigrator(projectPath, {
          backup: options.backup,
          verbose: options.verbose,
          dryRun: options.dryRun,
          include: options.include,
          exclude: options.exclude,
          enableArchitectureRefactor: options.architectureRefactor,
          enableElementPlusMigration: options.elementPlus,
          enableViteOptimization: options.viteOptimization,
          enableErrorDiagnostic: options.errorDiagnostic,
          buildCommand: options.buildCommand
        });

        result = await migrator.migrate();
      } else {
        console.log(chalk.gray('使用基础迁移器'));

        migrator = new SassMigrator(projectPath, {
          backup: options.backup,
          verbose: options.verbose,
          dryRun: options.dryRun,
          include: options.include,
          exclude: options.exclude
        });

        result = await migrator.migrate();
      }

      if (!options.enhanced) {
        console.log('\n' + chalk.bold.green('✅ Sass 迁移完成!'));

        if (result.processedFiles > 0) {
          console.log(chalk.green(`已迁移 ${result.processedFiles} 个文件`));
        }

        if (result.errorFiles > 0) {
          console.log(chalk.red(`${result.errorFiles} 个文件迁移失败`));
        }

        if (result.processedFiles > 0 && !options.dryRun) {
          console.log('\n' + chalk.blue('💡 提示:'));
          console.log('  - 请检查迁移后的文件确保正确性');
          console.log('  - 运行项目构建测试兼容性');
          if (options.backup) {
            console.log('  - 如有问题可使用备份文件恢复');
          }
          console.log('  - 考虑使用 --enhanced 选项获得更好的迁移体验');
        }
      }

    } catch (error) {
      console.error(chalk.red('\n❌ Sass 迁移失败:'));
      console.error(chalk.red(error.message));
      
      if (error.message.includes('sass-migrator 不可用')) {
        console.log('\n' + chalk.yellow('💡 解决方案:'));
        console.log('  运行以下命令安装 sass-migrator:');
        console.log(chalk.cyan('  npm install -g sass-migrator'));
      }
      
      process.exit(1);
    }
  });

// 添加备份管理命令
program
  .command('restore-backups')
  .argument('<project-path>', '项目路径')
  .description('恢复 Sass 迁移的备份文件')
  .action(async (projectPath) => {
    try {
      const migrator = new SassMigrator(projectPath);
      await migrator.restoreBackups();
      console.log(chalk.green('✅ 备份文件恢复完成'));
    } catch (error) {
      console.error(chalk.red(`❌ 恢复备份失败: ${error.message}`));
      process.exit(1);
    }
  });

program
  .command('clean-backups')
  .argument('<project-path>', '项目路径')
  .description('清理 Sass 迁移产生的备份文件')
  .action(async (projectPath) => {
    try {
      const migrator = new SassMigrator(projectPath);
      await migrator.cleanBackups();
      console.log(chalk.green('✅ 备份文件清理完成'));
    } catch (error) {
      console.error(chalk.red(`❌ 清理备份失败: ${error.message}`));
      process.exit(1);
    }
  });

// 添加增强版迁移命令
program
  .command('enhanced')
  .argument('<project-path>', '项目路径')
  .description('🚀 使用增强版 Sass 迁移器（推荐）')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--no-backup', '不创建备份文件')
  .option('--verbose', '显示详细输出')
  .option('--include <patterns...>', 'Sass 文件匹配模式', ['**/*.scss', '**/*.sass'])
  .option('--exclude <patterns...>', '排除的文件模式', ['node_modules/**', 'dist/**', 'build/**'])
  .option('--no-architecture-refactor', '禁用架构重构')
  .option('--no-element-plus', '禁用 Element Plus 迁移')
  .option('--no-vite-optimization', '禁用 Vite 配置优化')
  .option('--no-error-diagnostic', '禁用错误诊断')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .action(async (projectPath, options) => {
    try {
      const migrator = new EnhancedSassMigrator(projectPath, {
        backup: options.backup,
        verbose: options.verbose,
        dryRun: options.dryRun,
        include: options.include,
        exclude: options.exclude,
        enableArchitectureRefactor: options.architectureRefactor,
        enableElementPlusMigration: options.elementPlus,
        enableViteOptimization: options.viteOptimization,
        enableErrorDiagnostic: options.errorDiagnostic,
        buildCommand: options.buildCommand
      });

      await migrator.migrate();

    } catch (error) {
      console.error(chalk.red('\n❌ 增强版 Sass 迁移失败:'));
      console.error(chalk.red(error.message));

      if (error.message.includes('sass-migrator 不可用')) {
        console.log('\n' + chalk.yellow('💡 解决方案:'));
        console.log('  运行以下命令安装 sass-migrator:');
        console.log(chalk.cyan('  npm install -g sass-migrator'));
      }

      process.exit(1);
    }
  });

program.parse();