#!/usr/bin/env node

/**
 * BuildFixer 使用示例
 * 演示如何使用 BuildFixer 类进行构建错误修复
 */

const path = require('path');
const chalk = require('chalk');
const BuildFixer = require('../src/buildFixer');
const AIRepairer = require('../src/aiRepairer');

async function main() {
  console.log(chalk.blue('🚀 BuildFixer 使用示例\n'));

  // 示例项目路径（请替换为实际项目路径）
  const projectPath = process.argv[2] || './test-project';
  
  console.log(chalk.gray(`项目路径: ${projectPath}`));

  try {
    // 示例 1: 基本使用
    console.log(chalk.yellow('\n📋 示例 1: 基本使用'));
    await basicUsage(projectPath);

    // 示例 2: 与 AIRepairer 集成
    console.log(chalk.yellow('\n📋 示例 2: 与 AIRepairer 集成'));
    await withAIRepairer(projectPath);

    // 示例 3: 自定义配置
    console.log(chalk.yellow('\n📋 示例 3: 自定义配置'));
    await customConfiguration(projectPath);

    // 示例 4: 错误处理
    console.log(chalk.yellow('\n📋 示例 4: 错误处理'));
    await errorHandling(projectPath);

  } catch (error) {
    console.error(chalk.red('❌ 示例执行失败:'), error.message);
    process.exit(1);
  }
}

/**
 * 示例 1: 基本使用
 */
async function basicUsage(projectPath) {
  console.log(chalk.gray('创建 BuildFixer 实例...'));
  
  const buildFixer = new BuildFixer(projectPath, {
    buildCommand: 'npm run build',
    maxRetries: 3
  });

  console.log(chalk.gray('检查 AI 服务状态...'));
  const providerStatus = buildFixer.getProviderStatus();
  console.log(chalk.cyan(`AI 服务状态: ${providerStatus.enabled ? '启用' : '禁用'}`));
  if (providerStatus.provider) {
    console.log(chalk.cyan(`当前提供商: ${providerStatus.provider}`));
  }

  // 模拟构建和修复过程（不实际执行）
  console.log(chalk.gray('模拟构建过程...'));
  console.log(chalk.green('✅ 基本使用示例完成'));
}

/**
 * 示例 2: 与 AIRepairer 集成
 */
async function withAIRepairer(projectPath) {
  console.log(chalk.gray('创建 AIRepairer 实例...'));
  
  const aiRepairer = new AIRepairer({
    maxTokens: 4000,
    temperature: 0.1,
    maxRetries: 3
  });

  console.log(chalk.gray('创建 BuildFixer 实例并集成 AIRepairer...'));
  
  const buildFixer = new BuildFixer(projectPath, {
    buildCommand: 'npm run build',
    maxRetries: 3,
    aiRepairer: aiRepairer
  });

  // 演示错误分类
  console.log(chalk.gray('演示错误分类功能...'));
  const sampleErrors = [
    {
      message: 'Cannot find module "element-ui"',
      type: 'webpack',
      file: 'src/components/Button.vue'
    },
    {
      message: 'Property "Vue" does not exist on type',
      type: 'typescript',
      file: 'src/main.ts'
    },
    {
      message: 'Vue.extend is not a function',
      type: 'javascript',
      file: 'src/utils/helper.js'
    }
  ];

  const categorizedErrors = buildFixer.categorizeErrors(sampleErrors);
  categorizedErrors.forEach(error => {
    console.log(chalk.cyan(`  ${error.file}: ${error.category} (${error.type})`));
  });

  console.log(chalk.green('✅ AIRepairer 集成示例完成'));
}

/**
 * 示例 3: 自定义配置
 */
async function customConfiguration(projectPath) {
  console.log(chalk.gray('使用自定义配置创建 BuildFixer...'));
  
  const buildFixer = new BuildFixer(projectPath, {
    buildCommand: 'yarn build',
    maxRetries: 5,
    maxTokens: 2000,
    temperature: 0.2
  });

  // 演示提示词生成
  console.log(chalk.gray('演示构建错误提示词生成...'));
  const sampleError = {
    type: 'vue',
    category: 'ui-library',
    message: 'el-button component not found',
    file: 'src/components/MyButton.vue',
    line: 10,
    column: 5
  };

  const sampleContent = `<template>
  <div>
    <el-button @click="handleClick">Click me</el-button>
  </div>
</template>

<script>
export default {
  name: 'MyButton',
  methods: {
    handleClick() {
      this.$message('Hello!')
    }
  }
}
</script>`;

  const prompt = buildFixer.generateBuildErrorPrompt(sampleContent, sampleError);
  console.log(chalk.cyan('生成的提示词长度:'), prompt.length);
  console.log(chalk.cyan('包含关键词:'), 
    prompt.includes('UI 库兼容性错误修复') ? '✅' : '❌'
  );

  console.log(chalk.green('✅ 自定义配置示例完成'));
}

/**
 * 示例 4: 错误处理
 */
async function errorHandling(projectPath) {
  console.log(chalk.gray('演示错误处理机制...'));
  
  const buildFixer = new BuildFixer(projectPath, {
    buildCommand: 'invalid-command',
    maxRetries: 1
  });

  // 演示错误检测
  console.log(chalk.gray('测试错误检测功能...'));
  const errorLines = [
    'src/test.ts(10,5): error TS2304: Cannot find name "Vue".',
    'src/components/Test.vue:25:10: Element UI component not found',
    'ERROR in src/main.js',
    'src/test.js:15:8: error Unexpected token',
    'This is not an error line'
  ];

  errorLines.forEach(line => {
    const error = buildFixer.detectErrorStart(line);
    if (error) {
      console.log(chalk.cyan(`  检测到错误: ${error.type} - ${error.file || 'unknown'}`));
    } else {
      console.log(chalk.gray(`  非错误行: ${line.slice(0, 30)}...`));
    }
  });

  // 演示内容验证
  console.log(chalk.gray('测试内容验证功能...'));
  const testCases = [
    {
      name: '正常内容',
      original: 'export default { name: "Test" }',
      repaired: 'export default { name: "TestFixed" }',
      expected: true
    },
    {
      name: '空内容',
      original: 'export default { name: "Test" }',
      repaired: '',
      expected: false
    },
    {
      name: '包含错误标记',
      original: 'export default { name: "Test" }',
      repaired: 'export default { name: "Test" }; // COMPILATION_ERROR',
      expected: false
    }
  ];

  testCases.forEach(testCase => {
    const result = buildFixer.validateRepairedContent(testCase.repaired, testCase.original);
    const status = result === testCase.expected ? '✅' : '❌';
    console.log(chalk.cyan(`  ${status} ${testCase.name}: ${result}`));
  });

  console.log(chalk.green('✅ 错误处理示例完成'));
}

/**
 * 显示使用帮助
 */
function showHelp() {
  console.log(chalk.blue('BuildFixer 使用示例'));
  console.log(chalk.gray('\n用法:'));
  console.log('  node examples/build-fixer-usage.js [project-path]');
  console.log(chalk.gray('\n参数:'));
  console.log('  project-path  项目路径（可选，默认为 ./test-project）');
  console.log(chalk.gray('\n环境变量:'));
  console.log('  DEEPSEEK_TOKEN   DeepSeek API Token');
  console.log('  GLM_TOKEN        GLM API Token');
  console.log('  OPENAI_API_KEY   OpenAI API Key');
  console.log(chalk.gray('\n示例:'));
  console.log('  DEEPSEEK_TOKEN=your_token node examples/build-fixer-usage.js /path/to/project');
}

// 检查是否请求帮助
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 运行示例
if (require.main === module) {
  main().catch(error => {
    console.error(chalk.red('❌ 示例执行失败:'), error.message);
    process.exit(1);
  });
}

module.exports = {
  basicUsage,
  withAIRepairer,
  customConfiguration,
  errorHandling
};
