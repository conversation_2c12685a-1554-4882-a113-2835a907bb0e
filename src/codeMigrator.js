const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const glob = require('glob')
const gogocode = require('gogocode')
const { transform: vueTransform } = require('gogocode-plugin-vue')
const { transform: elementTransform } = require('gogocode-plugin-element')
const MigrationStrategySelector = require('./migrator/migrationStrategySelector');
const MigrationDocGenerator = require('./migrator/migrationDocGenerator');
const FailureLogger = require('./failureLogger');
const VueTransformer = require('./transform/vueTransformer');

/**
 * Vue 代码迁移器
 * 支持从源目录迁移到目标目录，或原地修改
 */
class CodeMigrator {
	constructor (inputPath, options = {}) {
		this.inputPath = path.resolve(inputPath)
		this.options = Object.assign({
			srcDir: 'src',
			outputSrcDir: 'src',
			includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
			excludePatterns: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.*/**'],
			isOutputMode: false,
			outputPath: '',
			aiApiKey: '',
			aiProvider: 'auto',
			buildCommand: 'npm run build',
			verbose: false,
			enableMigrationStrategy: true
		}, options)
		this.isOutputMode = this.options.isOutputMode
		this.outputPath = this.options.outputPath
		this.projectPath = this.inputPath
		this.stats = {
			success: 0,
			failed: 0,
			skipped: 0,
			copied: 0,
			total: 0,
			failedFiles: []
		}
		this.failureLogger = new FailureLogger(this.inputPath);

		// 迁移策略相关
		this.migrationStrategy = null
		this.analysisResult = null

		// 初始化转换器
		this.vueTransformer = new VueTransformer();
	}

	/**
	 * 执行迁移
	 */
	async migrate () {
		console.log(chalk.blue('🔄 开始 Vue 代码迁移 (原地修改模式)...'))

		try {
			// 确保输出目录存在
			await this.ensureOutputDirectory()

			// 复制 gogocode 转换工具
			await this.copyGogocodeTransferFile()

			// 获取需要迁移的文件列表
			const files = await this.getFilesToMigrate()
			console.log(chalk.gray(`找到 ${files.length} 个文件需要迁移`))

			// 设置总文件数
			this.stats.total = files.length

			// 初始化 FailureLogger 日志目录
			await this.failureLogger.initialize();

			// 只有在启用迁移策略时才执行分析
			if (this.options.enableMigrationStrategy) {
				// 执行迁移策略分析
				await this.performMigrationStrategyAnalysis()
			}

			// 执行 Gogocode 转换
			await this.performGogocodeTransformation(files)

			// 清理多余的 gogocodeTransfer.js 文件
			await this.cleanupGogocodeTransferFiles()

			// 保存迁移日志
			if (this.failureLogger) {
				await this.failureLogger.saveFailures();
			}

			// 打印迁移统计
			this.printMigrationStats()

			return this.stats

		} catch (error) {
			console.error(chalk.red('❌ 迁移失败:'), error.message)
			throw error
		}
	}

	/**
	 * 使用 Gogocode 批量转换文件
	 */
	async performGogocodeTransformation(files) {
		console.log(chalk.gray(`开始使用 Gogocode 转换 ${files.length} 个文件...`));

		// 迁移文件
		for (const filePath of files) {
			await this.migrateFile(filePath)
		}

		console.log(chalk.green(`✅ Gogocode 转换完成: 成功 ${this.stats.success}, 失败 ${this.stats.failed}`));

		if (this.stats.failed > 0) {
			console.log(chalk.yellow(`⚠️  发现 ${this.stats.failed} 个转换失败的文件，将在后续步骤中使用 AI 修复`));
		}
	}

	/**
	 * 执行迁移策略分析（仅针对失败文件）
	 */
	async performMigrationStrategyAnalysis() {
		try {
			// 只有在有失败文件时才进行分析
			if (this.stats.failedFiles.length === 0) {
				console.log(chalk.gray('🎉 没有失败的文件!'));
				return;
			}

			console.log(chalk.blue(`🎯 分析 ${this.stats.failedFiles.length} 个失败文件...`));

			// 只有在有失败文件且有 AI API Key 时才进行 AI 分析
			if (!this.options.aiApiKey) {
				console.log(chalk.yellow('⚠️  未提供 AI API Key，跳过 AI 分析'));
				return;
			}

			const strategySelector = new MigrationStrategySelector(this.inputPath, {
				aiApiKey: this.options.aiApiKey,
				verbose: this.options.verbose,
				failedFiles: this.stats.failedFiles // 传递失败文件信息
			});

			const strategyResult = await strategySelector.selectStrategy();
			this.migrationStrategy = strategyResult.strategy;
			this.analysisResult = strategyResult.analysisResult;

			// 根据策略执行相应的操作
			if (this.migrationStrategy === 'ai-assisted') {
				console.log(chalk.green('🤖 将使用 AI 辅助修复失败文件'));
				// AI 辅助策略专门针对失败文件
				const migrationPlan = await strategySelector.executeAIAssistedStrategy();
				this.migrationPlan = migrationPlan;
			} else if (this.migrationStrategy === 'documentation-guided') {
				console.log(chalk.yellow('📖 将使用文档指导修复失败文件'));

				// 生成迁移指导文档
				if (this.options.generateMigrationDoc) {
					const docGenerator = new MigrationDocGenerator(this.inputPath, this.analysisResult, {
						outputPath: path.join(this.inputPath, 'migration-guide.md')
					});
					await docGenerator.generateMigrationGuide();
				}

				const migrationGuide = await strategySelector.executeDocumentationGuidedStrategy();
				this.migrationGuide = migrationGuide;
			}

			console.log(chalk.green('✅ 失败文件分析完成'));
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  失败文件分析失败: ${error.message}`));
			console.log(chalk.gray('将跳过 AI 修复步骤'));
		}
	}

	/**
	 * 确保输出目录存在
	 */
	async ensureOutputDirectory () {
		if (this.isOutputMode) {
			const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
			await fs.ensureDir(outputSrcPath)
			console.log(chalk.gray(`已创建输出目录: ${outputSrcPath}`))
		}
	}

	/**
	 * 复制 gogocodeTransfer.js 工具文件到目标项目
	 *
	 * - '@/utils/gogocodeTransfer'
	 * - vue-count-to to vue3-count-to
	 */
	async copyGogocodeTransferFile () {
		try {
			// gogocodeTransfer.js 源文件路径（在迁移工具中）
			const sourceTransferFile = path.join(__dirname, 'utils', 'gogocodeTransfer.js')

			// 目标路径
			const targetPath = this.isOutputMode ? this.outputPath : this.inputPath
			const targetUtilsDir = path.join(targetPath, this.options.outputSrcDir || this.options.srcDir, 'utils')
			const targetTransferFile = path.join(targetUtilsDir, 'gogocodeTransfer.js')

			// 检查源文件是否存在
			if (await fs.pathExists(sourceTransferFile)) {
				// 确保目标 utils 目录存在
				await fs.ensureDir(targetUtilsDir)

				// 复制文件
				await fs.copy(sourceTransferFile, targetTransferFile)
				console.log(chalk.green(`✅ 已复制 gogocodeTransfer.js 到: ${path.relative(targetPath, targetTransferFile)}`))
			} else {
				console.log(chalk.yellow(`⚠️  未找到 gogocodeTransfer.js 源文件: ${sourceTransferFile}`))
			}
		} catch (error) {
			console.log(chalk.yellow(`⚠️  复制 gogocodeTransfer.js 失败: ${error.message}`))
		}
	}

	/**
	 * 获取需要迁移的文件列表
	 */
	async getFilesToMigrate () {
		const srcPath = path.join(this.inputPath, this.options.srcDir)
		const files = []

		// 检查源目录是否存在
		if (!await fs.pathExists(srcPath)) {
			throw new Error(`源目录不存在: ${srcPath}`)
		}

		for (const pattern of this.options.includePatterns) {
			const matchedFiles = glob.sync(pattern, {
				cwd: srcPath,
				ignore: this.options.excludePatterns,
				absolute: false
			})

			files.push(...matchedFiles.map(file => path.join(srcPath, file)))
		}

		// 去重
		return [...new Set(files)]
	}

	/**
	 * 迁移单个文件
	 */
	async migrateFile (filePath) {
		try {
			const relativePath = path.relative(this.inputPath, filePath)
			process.stdout.write(chalk.gray(`迁移: ${relativePath} ... `))

			// 读取文件内容
			const source = await fs.readFile(filePath, 'utf8')

			// 根据文件类型选择迁移策略
			let transformedCode
			const ext = path.extname(filePath)

			if (ext === '.vue' || ext === '.js' ) {
				transformedCode = await this.migrateVueFile(source, filePath)
				if (ext == ".js") {
					transformedCode = await this.migrateJsFile(transformedCode, filePath)
				}
			} else if (ext === '.ts') {
				transformedCode = await this.migrateJsFile(source, filePath)
			} else {
				console.log(chalk.yellow('跳过'))
				this.stats.skipped++
				await this.failureLogger.logSkipped(filePath, `不支持的文件类型: ${ext}`)
				return
			}

			// 确定输出文件路径
			const outputFilePath = this.getOutputFilePath(filePath)

			// 写入转换后的代码
			if (transformedCode && (transformedCode !== source || this.isOutputMode)) {
				// 确保输出目录存在
				await fs.ensureDir(path.dirname(outputFilePath))

				await fs.writeFile(outputFilePath, transformedCode, 'utf8')

				if (this.isOutputMode && outputFilePath !== filePath) {
					console.log(chalk.green('✅ (复制+转换)'))
					this.stats.copied++
					await this.failureLogger.logSuccess(filePath, {
						type: 'copy_and_transform',
						outputPath: outputFilePath,
						hasChanges: true
					})
				} else {
					console.log(chalk.green('✅'))
				}
				this.stats.success++
				await this.failureLogger.logSuccess(filePath, {
					type: 'transform',
					hasChanges: true,
					originalSize: source.length,
					transformedSize: transformedCode.length
				})
			} else {
				if (this.isOutputMode) {
					// 输出模式下，即使没有变化也要复制文件
					await fs.ensureDir(path.dirname(outputFilePath))
					await fs.copy(filePath, outputFilePath)
					console.log(chalk.gray('复制（无变化）'))
					this.stats.copied++
					await this.failureLogger.logSuccess(filePath, {
						type: 'copy_only',
						outputPath: outputFilePath,
						hasChanges: false
					})
				} else {
					console.log(chalk.gray('跳过（无变化）'))
				}
				this.stats.skipped++
				await this.failureLogger.logSkipped(filePath, '文件内容无变化')
			}

		} catch (error) {
			console.log(chalk.red('❌'))
			this.stats.failed++

			// 确保错误信息是字符串
			const errorMessage = typeof error === 'string' ? error :
								(error && error.message) ? error.message :
								(error && typeof error === 'object') ? JSON.stringify(error) :
								String(error);

			// 记录详细的失败信息，包括绝对路径
			const failureInfo = {
				file: path.relative(this.inputPath, filePath),
				absolutePath: filePath,
				error: errorMessage,
				errorType: this.categorizeError(errorMessage),
				timestamp: new Date().toISOString()
			}

			this.stats.failedFiles.push(failureInfo)

			// 记录失败到 FailureLogger
			await this.failureLogger.logFailure(filePath, errorMessage, {
				fileType: path.extname(filePath),
				errorType: this.categorizeError(errorMessage)
			})

			// 如果是 Gogocode 相关错误，记录更多信息
			if (errorMessage.includes('gogocode') || errorMessage.includes('eventsApi')) {
				console.log(chalk.yellow(`    Gogocode 转换错误: ${failureInfo.file}`))
			}

			// 添加调试信息
			if (this.options.verbose) {
				console.log(chalk.gray(`    错误类型: ${failureInfo.errorType}`))
				console.log(chalk.gray(`    错误详情: ${errorMessage.substring(0, 100)}...`))
			}
		}
	}

	/**
	 * 获取输出文件路径
	 */
	getOutputFilePath (inputFilePath) {
		if (!this.isOutputMode) {
			return inputFilePath
		}

		// 计算相对于输入源目录的路径
		const inputSrcPath = path.join(this.inputPath, this.options.srcDir)
		const relativeToSrc = path.relative(inputSrcPath, inputFilePath)

		// 构建输出路径
		const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
		return path.join(outputSrcPath, relativeToSrc)
	}

	/**
	 * 迁移 Vue 文件
	 */
	async migrateVueFile (source, filePath) {
		try {
			let transformedCode = source

			// 使用 gogocode-plugin-vue 进行转换
			try {
				// 1. 首先使用官方的 vueTransform 进行转换
				transformedCode = vueTransform(
					{
						path: filePath,
						source: source,
					},
					{
						gogocode: gogocode,
					},
					{
						rootPath: path.resolve(this.projectPath, this.options.srcDir),
						outFilePath: filePath,
						outRootPath: path.dirname(filePath),
					}
				)

				// vueTransform 直接返回转换后的代码字符串
				if (typeof transformedCode !== 'string') {
					console.warn(chalk.yellow(`Vue 转换返回非字符串类型: ${typeof transformedCode}`))
					transformedCode = source // 如果转换失败，使用原始代码
				}

				// 2. 然后使用 elementTransform 进行 Element UI 转换
				try {
					transformedCode = elementTransform(
						{
							path: filePath,
							source: transformedCode,
						},
						{
							gogocode: gogocode,
						},
						{
							rootPath: path.resolve(this.projectPath, this.options.srcDir),
							outFilePath: filePath,
							outRootPath: path.dirname(filePath),
						}
					)

					if (typeof transformedCode !== 'string') {
						console.warn(chalk.yellow(`Element 转换返回非字符串类型: ${typeof transformedCode}`))
						// 不回退到原始代码，保留 Vue 转换的结果
					}
				} catch (elementError) {
					console.warn(chalk.yellow(`Element 转换警告: ${elementError.message}`))
					// 保留 Vue 转换的结果，继续下一步
				}

			} catch (vueError) {
				console.warn(chalk.yellow(`Vue 转换警告: ${vueError.message}`))
				transformedCode = source
			}

			// 3. 最后应用自定义转换规则
			transformedCode = this.vueTransformer.transform(transformedCode)

			// 检查是否为关键文件且转换失败
			const isKeyFile = this.isKeyFile(filePath)
			const hasChanges = transformedCode !== source

			if (isKeyFile && !hasChanges) {
				// 标记关键文件为需要 AI 修复
				this.markForAIRepair(filePath, '关键文件自动转换失败')
				console.log(chalk.yellow(`⚠️  关键文件转换失败，已标记为 AI 修复: ${path.relative(this.projectPath, filePath)}`))
			}

			// 返回转换后的代码字符串，而不是 boolean 值
			return transformedCode

		} catch (error) {
			console.error(chalk.red(`❌ 迁移文件失败: ${filePath}`), error.message)
			this.stats.failed++
			this.stats.failedFiles.push({
				file: path.relative(this.projectPath, filePath),
				absolutePath: filePath,
				error: error.message
			})
			// 返回原始代码而不是 false
			return source
		}
	}

	/**
	 * 检查是否为关键文件
	 */
	isKeyFile (filePath) {
		const keyFiles = [
			'src/main.js',
			'src/main.ts',
			'src/App.vue',
			'src/router/index.js',
			'src/router/index.ts',
			'src/store/index.js',
			'src/store/index.ts'
		]

		const relativePath = path.relative(this.projectPath, filePath)

		// 调试输出
		if (this.options.verbose) {
			console.log(chalk.gray(`检查关键文件: ${relativePath}`))
		}

		return keyFiles.some(keyFile => {
			const isMatch = relativePath === keyFile || relativePath.endsWith(keyFile)
			if (this.options.verbose && isMatch) {
				console.log(chalk.blue(`✅ 识别为关键文件: ${relativePath}`))
			}
			return isMatch
		})
	}

	/**
	 * 标记文件为需要 AI 修复
	 */
	markForAIRepair (filePath, reason) {
		const relativePath = path.relative(this.projectPath, filePath)

		// 添加到需要 AI 修复的文件列表
		if (!this.filesForAIRepair) {
			this.filesForAIRepair = []
		}

		this.filesForAIRepair.push({
			file: relativePath,
			absolutePath: filePath,
			error: reason,
			type: 'key-file-conversion-failed'
		})
	}

	/**
	 * 获取需要 AI 修复的文件列表
	 */
	getFilesForAIRepair () {
		return this.filesForAIRepair || []
	}

	/**
	 * 迁移 JS/TS 文件
	 */
	async migrateJsFile (source, filePath) {
		try {
			let transformedCode = source

			// 使用 gogocode 进行 AST 转换
			try {
				const ast = gogocode(source)
				transformedCode = ast.generate()
			} catch (gogocodeError) {
				console.warn(chalk.yellow(`Gogocode 转换警告: ${gogocodeError.message}`))
				transformedCode = source
			}

			// 强制应用自定义转换规则（包括 Vue 2 到 Vue 3 的转换）
			// transformedCode = new VueTransformer().transform(transformedCode)

			// 检查是否为关键文件且转换失败
			const isKeyFile = this.isKeyFile(filePath)
			const hasChanges = transformedCode !== source

			if (isKeyFile && !hasChanges) {
				// 标记关键文件为需要 AI 修复
				this.markForAIRepair(filePath, '关键文件自动转换失败')
				console.log(chalk.yellow(`⚠️  关键文件转换失败，已标记为 AI 修复: ${path.relative(this.projectPath, filePath)}`))
			}

			return transformedCode
		} catch (error) {
			throw new Error(`JS 文件转换失败: ${error.message}`)
		}
	}

	/**
	 * 打印迁移统计
	 */
	printMigrationStats () {
		console.log('\n' + chalk.bold('📊 代码迁移统计:'))
		console.log(`总计: ${this.stats.total} 个文件`)
		console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`))
		console.log(chalk.gray(`⏸️  跳过: ${this.stats.skipped} 个`))
		console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`))

		if (this.isOutputMode) {
			console.log(chalk.blue(`📁 复制: ${this.stats.copied} 个文件`))
			console.log(chalk.gray(`输出路径: ${this.outputPath}`))
		}

		if (this.stats.failedFiles.length > 0) {
			console.log(chalk.red('\n失败的文件:'))
			this.stats.failedFiles.forEach(({ file, error }) => {
				console.log(`  ${file}: ${error}`)
			})
		}

		const successRate = ((this.stats.success / this.stats.total) * 100).toFixed(1)
		console.log(chalk.bold(`\n成功率: ${successRate}%`))
	}

	/**
	 * 获取失败的文件列表（用于 AI 修复）
	 */
	getFailedFiles() {
		return this.stats.failedFiles
	}

	/**
	 * 获取迁移策略
	 */
	getMigrationStrategy() {
		return this.migrationStrategy
	}

	/**
	 * 获取分析结果
	 */
	getAnalysisResult() {
		return this.analysisResult
	}

	/**
	 * 获取迁移计划（AI 辅助策略）
	 */
	getMigrationPlan() {
		return this.migrationPlan
	}

	/**
	 * 获取迁移指南（文档指导策略）
	 */
	getMigrationGuide() {
		return this.migrationGuide
	}

	/**
	 * 分类错误类型
	 */
	categorizeError (errorMessage) {
		if (errorMessage.includes('eventsApi') || errorMessage.includes('gogocode-plugin-vue')) {
			return 'gogocode-events-api'
		} else if (errorMessage.includes('gogocode')) {
			return 'gogocode-general'
		} else if (errorMessage.includes('SyntaxError')) {
			return 'syntax-error'
		} else if (errorMessage.includes('Cannot read properties')) {
			return 'property-access-error'
		} else {
			return 'unknown'
		}
	}

	/**
	 * 清理多余的 gogocodeTransfer.js 文件
	 */
	async cleanupGogocodeTransferFiles() {
		try {
			console.log(chalk.blue('🧹 清理多余的 gogocodeTransfer.js 文件...'))

			// 查找所有的 gogocodeTransfer.js 文件
			const gogocodeTransferFiles = glob.sync('**/utils/gogocodeTransfer.js', {
				cwd: this.inputPath,
				absolute: true,
				ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
			})

			// 保留主文件路径
			const mainGogocodeTransferFile = path.join(this.inputPath, 'src', 'utils', 'gogocodeTransfer.js')

			let cleanedCount = 0
			for (const filePath of gogocodeTransferFiles) {
				// 如果不是主文件，则删除
				if (filePath !== mainGogocodeTransferFile) {
					try {
						await fs.remove(filePath)
						cleanedCount++

						// 如果 utils 目录为空，也删除它
						const utilsDir = path.dirname(filePath)
						const utilsDirContents = await fs.readdir(utilsDir)
						if (utilsDirContents.length === 0) {
							await fs.remove(utilsDir)
						}
					} catch (error) {
						console.warn(chalk.yellow(`⚠️  删除文件失败: ${path.relative(this.inputPath, filePath)}`))
					}
				}
			}

			if (cleanedCount > 0) {
				console.log(chalk.green(`✅ 已清理 ${cleanedCount} 个多余的 gogocodeTransfer.js 文件`))
			} else {
				console.log(chalk.gray('📝 没有发现多余的 gogocodeTransfer.js 文件'))
			}

		} catch (error) {
			console.warn(chalk.yellow(`⚠️  清理 gogocodeTransfer.js 文件时出错: ${error.message}`))
		}
	}
}

module.exports = CodeMigrator
