# Vue 2 到 Vue 3 迁移工具

我正在实现一个 Vue 2 到 Vue 3 的迁移工具，以下是一些常用的 Vue 2 插件和它们在 Vue 3 中的替代品。

我希望能将这些信息整合到我的迁移工具中，以便在迁移过程中提供参考。这些插件的替代品可能会有不同的 API 或用法，因此在迁移时需要特别注意。

## 目标

针对每个插件，你应该：

- 通过链接读取官方文档，了解新 API 的用法，并向用户编写迁移指南
- 如果可能的话，提供示例代码，展示如何在 Vue 3 中使用新的替代插件
- 生成的目标文档应该是 Markdown 格式，便于用户查看和使用，使用 frontMatter 语法来描述插件信息，如源、目标库、链接等
- 确保在文档中包含示例代码和使用说明，以便用户可以快速上手
- 目标文件名以源插件名命名，使用 kebab-case 格式。方便未来直接在 dependencyUpgrader 使用以及不能启用 AI 修复的时候提供非智能化建议

## Vue 2 插件迁移对照表

### 基础组件

#### vue-splitpane → splitpanes
- **源插件**: vue-splitpane
- **目标插件**: [splitpanes](https://github.com/antoniandre/splitpanes)
- **状态**: 需要创建迁移文档

#### vue-count-to → vue3-count-to
- **源插件**: [vue-count-to](https://github.com/PanJiaChen/vue-countTo)
- **目标插件**: [vue3-count-to](https://github.com/xiaofan9/vue-count-to)
- **状态**: 需要创建迁移文档

#### vuedraggable → vue-draggable-next
- **源插件**: vuedraggable
- **目标插件**: [vue.draggable.next](https://github.com/SortableJS/vue.draggable.next)
- **状态**: 需要创建迁移文档

### UI 组件库

#### vue-calendar-component, vue-sweet-calendar → Element Plus
- **源插件**: vue-calendar-component, vue-sweet-calendar
- **目标插件**: [Element Plus Calendar](https://element-plus.org/zh-CN/component/calendar.html)
- **使用方式**:
  ```typescript
  import type { CalendarDateType, CalendarInstance } from 'element-plus'
  ```
- **状态**: 需要创建迁移文档

#### @riophae/vue-treeselect → Element Plus Tree Select
- **源插件**: @riophae/vue-treeselect
- **目标插件**: [Element Plus Tree Select](https://element-plus.org/zh-CN/component/tree-select/)
- **使用示例**:
  ```vue
  <template>
    <el-tree-select v-model="value" :data="data" style="width: 240px" />
  </template>
  ```
- **状态**: 需要创建迁移文档

### 图表组件

#### v-charts → vue-charts
- **源插件**: v-charts
- **目标插件**: [vue-charts](https://github.com/ecomfe/vue-echarts)
- **状态**: 需要创建迁移文档

### 滚动条组件

#### vue-scrollbars → vue3-perfect-scrollbar
- **源插件**: vue-scrollbars
- **目标插件**: [vue3-perfect-scrollbar](https://github.com/mercs600/vue3-perfect-scrollbar)
- **状态**: 需要创建迁移文档

### 工具库

#### vue-uuid → vue3-uuid
- **源插件**: vue-uuid
- **目标插件**: [vue3-uuid](https://github.com/wang90/vue3-uuid)
- **使用示例**:
  ```vue
  import UUID from "vue3-uuid";
  const app = createApp(App)
  app.use(UUID)
  ```
- **状态**: 需要创建迁移文档

### 富文本编辑器

#### @tinymce/tinymce-vue → @tinymce/tinymce-vue
- **源插件**: @tinymce/tinymce-vue
- **目标插件**: [@tinymce/tinymce-vue](https://github.com/tinymce/tinymce-vue)
- **状态**: 需要创建迁移文档

#### @wangeditor/editor-for-vue → @wangeditor/editor, @wangeditor/editor-for-vue@next
- **源插件**: @wangeditor/editor-for-vue
- **目标插件**: [@wangeditor/editor](https://www.wangeditor.com/v5/for-frame.html), @wangeditor/editor-for-vue@next
- **状态**: 需要创建迁移文档

### 文本格式化

#### vue-text-format → vue-format
- **源插件**: [vue-text-format](https://github.com/13601313270/vue-format)
- **目标插件**: [vue-text-format](https://github.com/13601313270/vue-format)
- **使用示例**:
  ```vue
  import Vue, { createApp } from 'vue'
  import format from 'vue-text-format';
  import App from './App.vue';
  const app = createApp(App)

  app
    .use(format)
    .mount('#app')
  ```
- **状态**: 需要创建迁移文档

### PDF 查看器

#### vuepdf → vue3-pdfjs
- **源插件**: vuepdf
- **目标插件**: [vue3-pdfjs](https://github.com/randolphtellis/vue3-pdfjs)
- **使用示例**:
  ```vue
  import { createApp } from 'vue'
  import App from './App.vue'
  import VuePdf from 'vue3-pdfjs'

  const app = createApp(App)
  app.use(VuePdf)
  app.mount('#app')
  ```
- **状态**: 需要创建迁移文档

### JSON 格式化

#### vue-json-pretty
- **源插件**: vue-json-pretty (Vue 2 use vue-json-pretty@v1-latest)
- **目标插件**: [vue-json-pretty](https://www.npmjs.com/package/vue-json-pretty)
- **状态**: 需要创建迁移文档

### 组织架构图

#### vue2-tree-org → vue3-tree-org
- **源插件**: vue2-tree-org
- **目标插件**: [vue3-tree-org](https://gitee.com/sangtian152/vue3-tree-org)
- **状态**: 需要创建迁移文档

### 编译器

#### vue-template-compiler → @vue/compiler-sfc
- **源插件**: vue-template-compiler
- **目标插件**: [@vue/compiler-sfc](https://www.npmjs.com/package/@vue/compiler-sfc)
- **状态**: 已完成

## 下一步计划

1. 为每个插件创建详细的迁移文档
2. 集成到 dependencyUpgrader 中
3. 提供 AI 修复和非智能化建议
4. 完善迁移工具的功能
