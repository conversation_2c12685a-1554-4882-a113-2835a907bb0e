---
title: '从 @tinymce/tinymce-vue (Vue 2) 迁移到 @tinymce/tinymce-vue (Vue 3)'
source: '@tinymce/tinymce-vue'
target: '@tinymce/tinymce-vue'
---

`@tinymce/tinymce-vue` 官方包同时支持 Vue 2 和 Vue 3，但通过不同的主版本号进行区分。迁移的核心是更新依赖版本，并采用 Vue 3 的标准组件使用方式。

- **Vue 2 用户**: 应使用 `tinymce-vue` 的 `v3` 版本。
- **Vue 3 用户**: 应使用 `tinymce-vue` 的 `v4` 或更高版本。

## 1. 升级依赖版本

首先，你需要在 `package.json` 中更新 `@tinymce/tinymce-vue` 的版本。

```bash
# 卸载旧版本
npm uninstall @tinymce/tinymce-vue

# 安装适用于 Vue 3 的新版本
npm install "@tinymce/tinymce-vue@^4" 
# 或者最新的 v5, v6 等
# npm install "@tinymce/tinymce-vue@latest"
```

## 2. 组件使用方式

迁移到 Vue 3 后，推荐使用组合式 API（Composition API）和 `<script setup>` 语法，这会让代码更简洁。

### Vue 2 用法 (Options API)

在 Vue 2 中，你可能会这样使用 `v-model` 和 `init` 对象：

```html
<template>
  <div id="app">
    <editor
      v-model="editorValue"
      :api-key="apiKey"
      :init="init"
    />
  </div>
</template>

<script>
import Editor from '@tinymce/tinymce-vue';

export default {
  name: 'App',
  components: {
    'editor': Editor
  },
  data() {
    return {
      apiKey: 'YOUR_API_KEY',
      editorValue: '<p>Initial content</p>',
      init: {
        height: 500,
        menubar: true,
        plugins: [
          'advlist autolink lists link image charmap print preview anchor',
          'searchreplace visualblocks code fullscreen',
          'insertdatetime media table paste code help wordcount'
        ],
        toolbar:
          'undo redo | formatselect | bold italic backcolor | \
          alignleft aligncenter alignright alignjustify | \
          bullist numlist outdent indent | removeformat | help'
      }
    };
  }
};
</script>
```

### Vue 3 用法 (Composition API)

在 Vue 3 中，我们可以使用 `ref` 来管理响应式数据，代码结构更清晰。

```html
<template>
  <main>
    <editor
      v-model="editorValue"
      :api-key="apiKey"
      :init="init"
    />
  </main>
</template>

<script setup>
import { ref } from 'vue';
import Editor from '@tinymce/tinymce-vue';

const apiKey = ref('YOUR_API_KEY');
const editorValue = ref('<p>Initial content for Vue 3</p>');

const init = {
  height: 500,
  menubar: true,
  plugins: [
    'advlist autolink lists link image charmap print preview anchor',
    'searchreplace visualblocks code fullscreen',
    'insertdatetime media table paste code help wordcount'
  ],
  toolbar:
    'undo redo | formatselect | bold italic backcolor | \
    alignleft aligncenter alignright alignjustify | \
    bullist numlist outdent indent | removeformat | help'
};
</script>
```

## 关键变更点总结

1.  **NPM 版本**: 这是最关键的一步。确保 `@tinymce/tinymce-vue` 的版本是 `4.x` 或更高。
2.  **数据绑定**: `v-model` 的工作方式在两个版本中保持一致，无缝对接。在 Vue 3 中，我们使用 `ref` 来创建响应式数据，而不是 `data()` 选项。
3.  **组件注册**: 在 `<script setup>` 中，不再需要 `components` 选项来注册组件，只需导入即可在模板中使用。
4.  **`init` 对象**: TinyMCE 的初始化配置对象 (`init` object) 的结构和可用选项保持不变。你之前的插件和工具栏配置可以几乎原封不动地迁移过来。

总的来说，迁移 `@tinymce/tinymce-vue` 的工作量很小。只要更新了 npm 包的版本，并遵循 Vue 3 的组件编写风格，就能平滑过渡。 