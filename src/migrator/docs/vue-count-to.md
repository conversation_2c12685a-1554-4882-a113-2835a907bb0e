---
source: vue-count-to
target: vue3-count-to
link: https://github.com/xiaofan9/vue-count-to
---

# 从 vue-count-to 迁移到 vue3-count-to

`vue3-count-to` 是 `vue-count-to` 针对 Vue 3 的升级版本，旨在提供一个平滑的迁移路径。它保留了原有的大部分 API，使得迁移过程非常简单。

## 主要区别

- **Vue 3 支持**: `vue3-count-to` 完全兼容 Vue 3。
- **API 兼容性**: 大部分 `vue-count-to` 的属性和方法在 `vue3-count-to` 中都得到了保留。

## 安装

首先，你需要卸载 `vue-count-to` 并安装 `vue3-count-to`：

```bash
npm uninstall vue-count-to
npm install vue3-count-to
```

或者使用 yarn:

```bash
yarn remove vue-count-to
yarn add vue3-count-to
```

## 使用方法

`vue3-count-to` 的使用方式与 `vue-count-to` 非常相似。

### 1. 注册组件

你可以选择全局注册或局部注册。

#### 全局注册 (在 `main.js` 中)

```javascript
import { createApp } from 'vue'
import App from './App.vue'
import countTo from 'vue3-count-to'

const app = createApp(App)
app.use(countTo)
app.mount('#app')
```

#### 局部注册

```vue
<template>
  <count-to :startVal="startVal" :endVal="endVal" :duration="3000"></count-to>
</template>

<script>
import { CountTo } from 'vue3-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      startVal: 0,
      endVal: 2023
    }
  }
}
</script>
```

如果使用 `<script setup>`:

```vue
<template>
  <count-to :start-val="0" :end-val="2023" :duration="3000" />
</template>

<script setup>
import { CountTo } from 'vue3-count-to';
</script>
```

### 2. 模板迁移

基本上，你只需要将 `vue-count-to` 的组件标签 `count-to` 或 `countTo` 替换为 `vue3-count-to` 的相应用法即可。由于 API 兼容，大部分属性绑定都可以直接保留。

**Vue 2 with `vue-count-to`:**
```html
<template>
  <countTo ref="counter" :startVal='startVal' :endVal='endVal' :duration='3000' @mountedCallback="onMounted"></countTo>
</template>
```

**Vue 3 with `vue3-count-to`:**
```vue
<template>
  <count-to ref="counter" :start-val="startVal" :end-val="endVal" :duration="3000" @mounted="onMounted"></count-to>
</template>

<script setup>
import { ref } from 'vue'

const startVal = ref(0)
const endVal = ref(2023)

const onMounted = () => {
  console.log('Component has been mounted!')
}
</script>
```
**注意**: 在 Vue 3 中，事件名建议使用 kebab-case 格式，例如 `mountedCallback` 变为 `mounted`。你需要查阅 `vue3-count-to` 的文档以确认具体的事件名。

## 属性和方法

`vue3-count-to` 保留了 `vue-count-to` 的大部分属性和方法。你可以继续使用 `startVal` (或 `start-val`), `endVal` (或 `end-val`), `duration`, `autoplay`, `decimals`, `separator`, `prefix`, `suffix` 等属性。

控制动画的方法如 `start()`, `pause()`, `reset()` 也可以通过 `ref` 调用。

### 通过 ref 控制

```vue
<template>
  <div>
    <count-to ref="counterRef" :end-val="1000" :autoplay="false" />
    <button @click="startCount">Start</button>
    <button @click="pauseCount">Pause</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { CountTo } from 'vue3-count-to'

const counterRef = ref(null)

const startCount = () => {
  counterRef.value?.start()
}

const pauseCount = () => {
  counterRef.value?.pause()
}
</script>
```

## 总结

从 `vue-count-to` 迁移到 `vue3-count-to` 是一个相对直接的过程。主要工作是更新依赖包和调整组件的导入和注册方式。由于 API 的高度兼容性，大部分现有的模板和逻辑都可以不做修改或只需少量修改即可正常工作。

更多详情请参考 [vue3-count-to 的 npm 页面](https://www.npmjs.com/package/vue3-count-to) 和其引用的 [vue-count-to 文档](https://github.com/PanJiaChen/vue-countTo)。 