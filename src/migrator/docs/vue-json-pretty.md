---
title: 迁移 vue-json-pretty
---

# 迁移指南: `vue-json-pretty`

本指南详细介绍了将 `vue-json-pretty` 从 Vue 2 项目迁移到 Vue 3 项目的过程。该库为 Vue 的每个版本提供了不同的主版本，因此迁移主要涉及更新包版本和适应一些 API 的更改。

## 主要变化

1.  **NPM 包版本**: Vue 2 项目使用 `v1.x` 版本的 `vue-json-pretty`，而 Vue 3 项目应使用最新的 `v2.x` 或更高版本。
2.  **事件名称**: 事件名称已从 kebab-case (`node-click`) 更新为 camelCase (`nodeClick`)，以更好地与现代 JavaScript 约定保持一致。
3.  **`.sync` 修饰符**: 已弃用的 `.sync` 修饰符（用于像 `selectedValue` 这样的 props）已被 `v-model` 替换。
4.  **CSS 导入**: 手动导入样式表的需求保持不变。

---

## 分步迁移指南

### 1. 更新 `package.json`

第一步是更新 `package.json` 文件中 `vue-json-pretty` 的版本，并安装新版本。

**对于 Vue 2，你的 `package.json` 会是这样:**
```json
"dependencies": {
  "vue-json-pretty": "v1-latest"
}
```

**对于 Vue 3，请更新到最新版本:**
```json
"dependencies": {
  "vue-json-pretty": "^2.4.0"
}
```
*（请在 npm 上检查最新的版本号）*

然后，运行你的包管理器的安装命令:
```bash
npm install
# 或者
yarn install
```

### 2. 检查 CSS 导入

组件的样式表不会自动捆绑。请确保你仍然在你的组件中或在应用程序的入口文件中全局导入它。这部分的设置没有改变。

```javascript
// 在你组件的 <script> 部分或在 main.js 中
import 'vue-json-pretty/lib/styles.css';
```

### 3. 更新组件用法

组件的基本用法保持不变。你导入它并将数据传递给 `:data` prop。

```vue

<template>
  <vue-json-pretty :data="jsonData"/>
</template>

<script setup>
  import { ref } from 'vue';
  import VueJsonPretty from 'src/migrator/docs/vue-json-pretty';
  import 'vue-json-pretty/lib/styles.css';

  const jsonData = ref({
    name: 'vue-json-pretty',
    version: '2.4.0',
  });
</script>
```

### 4. 更新事件处理器

如果你正在监听来自组件的事件，你需要将事件名称从 kebab-case 更新为 camelCase。

#### Vue 2 示例
```vue
<template>
  <vue-json-pretty
    :data="jsonData"
    @node-click="handleNodeClick"
  />
</template>
```

#### Vue 3 示例
```vue
<template>
  <vue-json-pretty
    :data="jsonData"
    @nodeClick="handleNodeClick"
  />
</template>
```
**事件名称变更列表:**
- `@node-click` → `@nodeClick`
- `@brackets-click` → `@bracketsClick`
- `@icon-click` → `@iconClick`
- `@selected-change` → `@selectedChange`


### 5. 更新 `.sync` 为 `v-model`

在 Vue 2 中，除了 `value` 之外的 props 的双向绑定通常使用 `.sync` 修饰符处理。在 Vue 3 中，这被标准化为 `v-model`。`vue-json-pretty` 对 `selectedValue` prop 使用了此功能。

#### Vue 2 示例 (`.sync`)
```vue
<template>
  <vue-json-pretty
    :data="jsonData"
    :selectedValue.sync="mySelectedValue"
  />
</template>
```

#### Vue 3 示例 (`v-model`)
```vue
<template>
  <vue-json-pretty
    :data="jsonData"
    v-model:selectedValue="mySelectedValue"
  />
</template>

<script setup>
import { ref } from 'vue';
// ... 其他导入
const mySelectedValue = ref('');
</script>
```

### 6. 更新自定义插槽

如果你使用插槽来自定义节点的渲染，插槽名称也已更新。

- `nodeKey` → `renderNodeKey`
- `nodeValue` → `renderNodeValue`

## 总结

迁移 `vue-json-pretty` 是一个直接的过程。通过更新你的包版本，将事件监听器更改为 camelCase，并将任何 `.sync` 修饰符转换为 `v-model`，你的 JSON 查看器将完全兼容 Vue 3。 
