---
title: '从 vue-scrollbars 迁移到 vue3-perfect-scrollbar'
source: 'vue-scrollbars'
target: 'vue3-perfect-scrollbar'
---

`vue3-perfect-scrollbar` 是 `perfect-scrollbar` 的 Vue 3 包装器，它替代了 Vue 2 生态中常用的 `vue2-perfect-scrollbar`（有时统称为 `vue-scrollbars`）。迁移过程相对直接，主要涉及包的更新、CSS 导入路径的更改以及组件使用方式的微小调整。

## 1. 安装新依赖

首先，你需要卸载旧的滚动条插件并安装新的 `vue3-perfect-scrollbar`。

```bash
npm un vue2-perfect-scrollbar
# 或者如果你使用的是其他类似的滚动条库
# npm un vue-scrollbars

npm i vue3-perfect-scrollbar
```

## 2. 全局注册和样式导入

在你的应用入口文件（通常是 `main.js` 或 `main.ts`）中，更新插件的注册方式和 CSS 导入。

### Vue 2 (main.js)

在 Vue 2 中，你可能是这样注册的：

```javascript
import Vue from 'vue';
import PerfectScrollbar from 'vue2-perfect-scrollbar';
import 'vue2-perfect-scrollbar/dist/vue2-perfect-scrollbar.css';

Vue.use(PerfectScrollbar);
```

### Vue 3 (main.js)

在 Vue 3 中，使用 `createApp` 返回的 app 实例来注册，并且 CSS 的导入路径也已改变：

```javascript
import { createApp } from 'vue';
import App from './App.vue';
import { PerfectScrollbarPlugin } from 'vue3-perfect-scrollbar';
import 'vue3-perfect-scrollbar/style.css'; // 导入新的 CSS 文件

const app = createApp(App);
app.use(PerfectScrollbarPlugin);
app.mount('#app');
```

**关键变更**:
- **包名**: 从 `vue2-perfect-scrollbar` 变为 `vue3-perfect-scrollbar`。
- **注册方式**: 从 `Vue.use(PerfectScrollbar)` 变为 `app.use(PerfectScrollbarPlugin)`。
- **CSS 导入**: 从 `vue2-perfect-scrollbar/dist/vue2-perfect-scrollbar.css` 变为 `vue3-perfect-scrollbar/style.css`。

## 3. 组件用法更新

在组件模板中，`vue3-perfect-scrollbar` 使用了 PascalCase 命名约定。

### Vue 2 示例

```html
<template>
  <perfect-scrollbar class="scroll-area">
    <!-- Content -->
  </perfect-scrollbar>
</template>

<style>
.scroll-area {
  position: relative;
  height: 300px;
}
</style>
```

### Vue 3 示例

你需要将组件标签从 `perfect-scrollbar` (kebab-case) 修改为 `PerfectScrollbar` (PascalCase)。

```html
<template>
  <PerfectScrollbar class="scroll-area">
    <!-- Content -->
  </PerfectScrollbar>
</template>

<script setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
// 如果是局部注册，请确保导入
</script>

<style>
/* 确保导入了全局 CSS 或在此局部导入 */
@import 'vue3-perfect-scrollbar/style.css';

.scroll-area {
  position: relative;
  height: 300px;
}
/* 注意：vue3-perfect-scrollbar 使用 .ps 作为根元素的 class */
.ps {
  height: 300px;
}
</style>
```

### 局部注册

如果你倾向于局部注册组件，用法如下：

```html
<template>
  <PerfectScrollbar class="scroll-area">
    <p>这里是大量内容...</p>
  </PerfectScrollbar>
</template>

<script setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
</script>

<style>
@import 'vue3-perfect-scrollbar/style.css';

.ps {
  max-height: 400px; /* 或 height: 400px; */
}
</style>
```

## 4. Props 和 Events

`vue3-perfect-scrollbar` 的 props 和 events 与 `perfect-scrollbar` 库基本保持一致。

- **`tag`**: 用来自定义渲染的 HTML 标签，默认为 `div`。
- **`options`**: 传递给 `perfect-scrollbar` 实例的配置对象。
- **Events**: 事件名称与 `perfect-scrollbar` 保持一致，例如 `@ps-scroll-y`。

```html
<template>
  <PerfectScrollbar :options="scrollbarOptions" @ps-scroll-y="onScroll">
    <!-- content -->
  </PerfectScrollbar>
</template>

<script setup>
const scrollbarOptions = {
  wheelSpeed: 0.5,
  suppressScrollX: true,
};

function onScroll(event) {
  console.log('Scrolling Y:', event);
}
</script>
```

迁移到 `vue3-perfect-scrollbar` 的过程很顺畅，主要是更新依赖和调整为 Vue 3 的 API 风格。 