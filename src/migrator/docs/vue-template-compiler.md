---
title: 迁移 vue-template-compiler
---

# 迁移指南: `vue-template-compiler` 到 `@vue/compiler-sfc`

本指南解释了从 `vue-template-compiler` 到 `@vue/compiler-sfc` 的迁移。与其他关注 UI 组件的指南不同，本指南处理的是 Vue 构建过程的核心部分。对于许多使用标准工具（如 Vue CLI 或 Vite）的开发者来说，此更改将在项目升级过程中自动处理。

然而，如果你有一个自定义的构建设置（例如，一个自定义的 Webpack 配置），理解这个变化是至关重要的。

## 编译器的角色

-   在 **Vue 2** 中，`vue-template-compiler` 是一个用于将 `.vue` 文件中的 `<template>` 块编译成 JavaScript `render` 函数的包。它的版本必须与核心 `vue` 包保持同步。

-   在 **Vue 3** 中，这个职责被分开了。新的 `@vue/compiler-sfc` 包负责解析单文件组件（SFCs），而实际的模板编译则由 `@vue/compiler-dom` 处理，它现在是 `vue` 包本身的核心依赖项。

从本质上讲，`@vue/compiler-sfc` 是构建工具用来理解和处理 `.vue` 文件的工具。

---

## 分步迁移指南

### 1. 依赖更新

第一步是调整你项目的 `devDependencies`。

1.  **移除 `vue-template-compiler`**: 在 Vue 3 项目中不再需要这个包。

    ```bash
    npm uninstall vue-template-compiler
    ```

2.  **检查 `@vue/compiler-sfc`**: 这个包是现代 Vue 构建插件（Webpack 的 `vue-loader`，Vite 的 `@vitejs/plugin-vue`）的对等依赖（peer dependency）。在大多数情况下，你 **不** 需要手动安装它。它会随着构建插件一起被安装。如果由于特定原因需要添加它，它应该是一个 `devDependency`。

    ```bash
    # 仅在你的特定设置需要时
    npm install -D @vue/compiler-sfc
    ```

### 2. 构建配置更新

这是最可能发生重大手动更改的地方，具体取决于你的构建工具。

#### 对于 Vue CLI 用户

当你使用官方迁移命令（`vue upgrade`）将项目升级到 Vue 3 时，Vue CLI 会自动更新你的 Webpack 配置、`vue-loader` 以及所有相关依赖。这个变更会自动为你处理。

#### 对于 Vite 用户

如果你正在使用 Vite 创建一个新的 Vue 3 项目或迁移现有项目，`@vitejs/plugin-vue` 插件会处理 SFC 编译。它在底层使用 `@vue/compiler-sfc`，你无需进行任何手动配置。

#### 对于自定义 Webpack 用户

如果你管理自己的 Webpack 配置，你需要进行以下更改：

1.  **更新 `vue-loader`**: 你必须将 `vue-loader` 更新到版本 16 或更高。版本 16 是为 Vue 3 设计的，并使用 `@vue/compiler-sfc`。

    ```bash
    npm install -D vue-loader@^16.0.0
    ```

2.  **更新 Webpack 配置**: `vue-loader` 插件的导入方式已更改。

    **Vue 2 `webpack.config.js`:**
    ```javascript
    const { VueLoaderPlugin } = require('vue-loader'); // 适用于 v15 及以下版本

    module.exports = {
      // ...
      plugins: [
        new VueLoaderPlugin(),
      ],
    };
    ```

    **Vue 3 `webpack.config.js`:**
    ```javascript
    const { VueLoaderPlugin } = require('vue-loader'); // 适用于 v16+

    module.exports = {
      // ...
      plugins: [
        new VueLoaderPlugin(),
      ],
    };
    ```
    虽然 import 语句可能看起来一样，但请确保你已经更新了 `vue-loader` 包的版本。新版本的加载器将自动使用 `@vue/compiler-sfc`，并且不需要 `vue-template-compiler`。

## 总结

从 `vue-template-compiler` 到 `@vue/compiler-sfc` 的过渡是 Vue 3 构建过程中的一个根本性转变。

-   如果你使用像 **Vue CLI** 或 **Vite** 这样的标准项目设置，这个迁移在很大程度上是 **自动的**。
-   如果你有一个 **自定义的 Webpack 设置**，关键是升级 `vue-loader` 到 v16+ 并移除现在已经过时的 `vue-template-compiler`。

通过确保你的构建工具为 Vue 3 正确配置，新的编译器将在幕后无缝工作。 