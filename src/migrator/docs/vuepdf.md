---
title: 迁移 vuepdf 到 vue3-pdfjs
---

# 迁移指南: `vuepdf` 到 `vue3-pdfjs`

本指南概述了将 `vue-pdf` (一个流行的 Vue 2 PDF 查看器组件) 迁移到 `vue3-pdfjs` 以用于 Vue 3 应用程序的步骤。

**重要提示:** 目标库 `vue3-pdfjs` 已有数年未积极维护。虽然本指南提供了迁移所需的步骤，但你应考虑为 Vue 3 使用更现代且积极维护的替代品，例如 **`@tato30/vue-pdf`** 或 **`pdf-vue3`**。这些库提供更好的支持、更多功能，并且更可能与最新的 Web 标准和 Vue 3 功能兼容。

## 主要差异

- **包名**: 你将从 `vue-pdf` 切换到 `vue3-pdfjs`。
- **插件注册**: `vue3-pdfjs` 需要在你的 Vue 3 应用程序中注册为插件。
- **组件名称**: 组件标签从 `<pdf>` 更改为 `<VuePdf>`。
- **API 和导入**: 两个库都使用 `createLoadingTask` 函数，但导入路径以及你与之交互的方式不同，尤其是在使用组合式 API 时。

---

## 分步迁移指南

### 1. 更新依赖

首先，卸载旧包并安装新包。

```bash
npm uninstall vue-pdf
npm install vue3-pdfjs
```

### 2. 注册 Vue 3 插件

在你的应用程序入口文件 (例如, `src/main.js`) 中，你需要注册 `vue3-pdfjs`。

```javascript
// src/main.js
import { createApp } from 'vue';
import App from './App.vue';
import VuePdf from 'vue3-pdfjs';

const app = createApp(App);
app.use(VuePdf);
app.mount('#app');
```

### 3. 更新组件用法

迁移将主要影响你在组件中渲染 PDF 的方式。以下示例展示了如何调整一个常见用例：加载 PDF 并显示其所有页面。

#### Vue 2 示例 (使用 `vue-pdf`)

在 Vue 2 中，你可能有一个使用选项式 API 的组件，如下所示。

```vue
<template>
  <div>
    <p>{{ currentPage }} / {{ pageCount }}</p>
    <pdf
      v-for="i in pageCount"
      :key="i"
      :src="src"
      :page="i"
      @page-loaded="currentPage = $event"
    ></pdf>
  </div>
</template>

<script>
import pdf from 'vue-pdf';

var loadingTask = pdf.createLoadingTask('./path/to/your.pdf');

export default {
  components: {
    pdf,
  },
  data() {
    return {
      src: loadingTask,
      currentPage: 0,
      pageCount: 0,
    };
  },
  mounted() {
    this.src.promise.then(pdf => {
      this.pageCount = pdf.numPages;
    });
  },
};
</script>
```

#### Vue 3 示例 (使用 `vue3-pdfjs`)

这是在 Vue 3 中使用组合式 API (`<script setup>`) 的等效实现。

```vue
<template>
  <div>
    <p v-if="pageCount > 0">第 {{ currentPage }} 页 / 共 {{ pageCount }} 页</p>
    <VuePdf
      v-for="page in pageCount"
      :key="page"
      :src="pdfSrc"
      :page="page"
      @loaded="onLoaded"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
// 注意 createLoadingTask 的导入路径
import { createLoadingTask } from 'vue3-pdfjs/esm';

const pdfSrc = ref('./path/to/your.pdf');
const pageCount = ref(0);
const currentPage = ref(0);

onMounted(() => {
  const loadingTask = createLoadingTask(pdfSrc.value);
  loadingTask.promise.then(pdf => {
    pageCount.value = pdf.numPages;
  });
});

// 一个简单的函数来跟踪当前页，类似于旧的 @page-loaded
function onLoaded(pdf) {
    currentPage.value = pdf.numPages;
}
</script>
```

### 变更摘要

-   **安装**: 更换 npm 包。
-   **注册**: 在 `main.js` 中添加 `app.use(VuePdf)`。
-   **组件标签**: 将 `<pdf>` 重命名为 `<VuePdf>`。注意大小写。
-   **`createLoadingTask`**: 从 `vue3-pdfjs/esm` 导入。
-   **Props 和事件**: `src` 和 `page` props 的工作方式类似。事件已更改；例如，`vue3-pdfjs` 没有 `@page-loaded` 事件。你可以使用 `@loaded` 事件来获取有关已加载 PDF 文档的信息。
-   **响应式**: 调整你的代码以使用 Vue 3 的响应式系统 (`ref`, `reactive`)。

通过遵循这些步骤，你可以将你的 PDF 查看功能迁移到 Vue 3。然而，由于 `vue3-pdfjs` 的维护状态，强烈建议为你的项目评估更现代的替代方案。 