const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

/**
 * 架构重构引擎
 * 自动重构 Sass 项目架构，生成桶文件，检测和修复循环依赖
 */
class SassArchitectureRefactor {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      stylesDir: 'src/styles',
      utilsDir: 'src/styles/utils',
      componentsDir: 'src/styles/components',
      include: ['**/*.scss', '**/*.sass'],
      exclude: ['node_modules/**', 'dist/**', 'build/**'],
      verbose: false,
      ...options
    };
    
    this.dependencyGraph = new Map();
    this.circularDependencies = [];
    this.variables = new Map();
    this.mixins = new Map();
    this.functions = new Map();
  }

  /**
   * 执行完整的架构重构
   */
  async refactor() {
    console.log(chalk.blue('🏗️  开始 Sass 架构重构...'));
    
    try {
      // 1. 分析现有结构
      await this.analyzeCurrentStructure();
      
      // 2. 检测循环依赖
      await this.detectCircularDependencies();
      
      // 3. 提取和组织共享代码
      await this.extractSharedCode();
      
      // 4. 生成桶文件
      await this.generateBarrelFiles();
      
      // 5. 更新文件引用
      await this.updateFileReferences();
      
      console.log(chalk.green('✅ Sass 架构重构完成'));
      
      return {
        circularDependencies: this.circularDependencies,
        variablesCount: this.variables.size,
        mixinsCount: this.mixins.size,
        functionsCount: this.functions.size
      };
      
    } catch (error) {
      console.error(chalk.red(`架构重构失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 分析现有结构
   */
  async analyzeCurrentStructure() {
    console.log(chalk.gray('分析现有 Sass 文件结构...'));
    
    const sassFiles = await this.findSassFiles();
    
    for (const filePath of sassFiles) {
      await this.analyzeFile(filePath);
    }
    
    if (this.options.verbose) {
      console.log(chalk.gray(`分析了 ${sassFiles.length} 个文件`));
    }
  }

  /**
   * 查找所有 Sass 文件
   */
  async findSassFiles() {
    const allFiles = [];
    
    for (const pattern of this.options.include) {
      const files = glob.sync(pattern, {
        cwd: this.projectPath,
        absolute: true,
        ignore: this.options.exclude
      });
      allFiles.push(...files);
    }
    
    return [...new Set(allFiles)];
  }

  /**
   * 分析单个文件
   */
  async analyzeFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const relativePath = path.relative(this.projectPath, filePath);
      
      // 提取依赖关系
      const dependencies = this.extractDependencies(content);
      this.dependencyGraph.set(relativePath, dependencies);
      
      // 提取变量、混入和函数
      this.extractVariables(content, relativePath);
      this.extractMixins(content, relativePath);
      this.extractFunctions(content, relativePath);
      
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`分析文件失败 ${filePath}: ${error.message}`));
      }
    }
  }

  /**
   * 提取文件依赖关系
   */
  extractDependencies(content) {
    const dependencies = [];
    
    // 匹配 @import 和 @use 语句
    const importRegex = /@(?:import|use)\s+['"]([^'"]+)['"](?:\s+as\s+[^;]+)?;?/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }
    
    return dependencies;
  }

  /**
   * 提取变量定义
   */
  extractVariables(content, filePath) {
    const variableRegex = /\$([a-zA-Z0-9_-]+)\s*:\s*([^;]+);/g;
    let match;
    
    while ((match = variableRegex.exec(content)) !== null) {
      const varName = match[1];
      const varValue = match[2].trim();
      
      if (!this.variables.has(varName)) {
        this.variables.set(varName, []);
      }
      
      this.variables.get(varName).push({
        file: filePath,
        value: varValue,
        hasDefault: varValue.includes('!default')
      });
    }
  }

  /**
   * 提取混入定义
   */
  extractMixins(content, filePath) {
    // 匹配带参数和不带参数的混入
    const mixinRegex = /@mixin\s+([a-zA-Z0-9_-]+)(?:\s*\([^)]*\))?\s*\{/g;
    let match;

    while ((match = mixinRegex.exec(content)) !== null) {
      const mixinName = match[1];

      if (!this.mixins.has(mixinName)) {
        this.mixins.set(mixinName, []);
      }

      this.mixins.get(mixinName).push({
        file: filePath
      });
    }
  }

  /**
   * 提取函数定义
   */
  extractFunctions(content, filePath) {
    const functionRegex = /@function\s+([a-zA-Z0-9_-]+)\s*\([^)]*\)\s*\{/g;
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1];
      
      if (!this.functions.has(functionName)) {
        this.functions.set(functionName, []);
      }
      
      this.functions.get(functionName).push({
        file: filePath
      });
    }
  }

  /**
   * 检测循环依赖
   */
  async detectCircularDependencies() {
    console.log(chalk.gray('检测循环依赖...'));
    
    const visited = new Set();
    const recursionStack = new Set();
    
    for (const [file] of this.dependencyGraph) {
      if (!visited.has(file)) {
        this.dfsDetectCycle(file, visited, recursionStack, []);
      }
    }
    
    if (this.circularDependencies.length > 0) {
      console.log(chalk.yellow(`⚠️  发现 ${this.circularDependencies.length} 个循环依赖`));
      
      if (this.options.verbose) {
        this.circularDependencies.forEach((cycle, index) => {
          console.log(chalk.yellow(`  循环 ${index + 1}: ${cycle.join(' -> ')}`));
        });
      }
    } else {
      console.log(chalk.green('✅ 未发现循环依赖'));
    }
  }

  /**
   * 深度优先搜索检测循环
   */
  dfsDetectCycle(file, visited, recursionStack, path) {
    visited.add(file);
    recursionStack.add(file);
    path.push(file);

    const dependencies = this.dependencyGraph.get(file) || [];

    for (const dep of dependencies) {
      // 解析依赖路径为相对路径
      let depPath = this.resolveDependencyPath(dep, file);

      // 如果解析失败，尝试直接使用依赖名称
      if (!depPath) {
        // 对于相对路径，尝试简单的路径拼接
        if (dep.startsWith('./') || dep.startsWith('../')) {
          const currentDir = path.dirname(file);
          depPath = path.normalize(path.join(currentDir, dep));
        } else {
          continue;
        }
      }

      // 确保路径在依赖图中存在
      if (!this.dependencyGraph.has(depPath)) {
        // 尝试添加 .scss 扩展名
        const scssPath = depPath + '.scss';
        if (this.dependencyGraph.has(scssPath)) {
          depPath = scssPath;
        } else {
          continue;
        }
      }

      if (!visited.has(depPath)) {
        this.dfsDetectCycle(depPath, visited, recursionStack, [...path]);
      } else if (recursionStack.has(depPath)) {
        // 发现循环依赖
        const cycleStart = path.indexOf(depPath);
        if (cycleStart !== -1) {
          const cycle = [...path.slice(cycleStart), depPath];
          this.circularDependencies.push(cycle);
        }
      }
    }

    recursionStack.delete(file);
  }

  /**
   * 解析依赖路径
   */
  resolveDependencyPath(dependency, currentFile) {
    // 简化的路径解析逻辑
    if (dependency.startsWith('./') || dependency.startsWith('../')) {
      const currentDir = path.dirname(currentFile);
      const resolved = path.resolve(this.projectPath, currentDir, dependency);
      const relativePath = path.relative(this.projectPath, resolved);
      
      // 确保路径是相对于项目根目录的
      if (relativePath.startsWith('..')) {
        return null;
      }
      
      return relativePath;
    }

    // 对于绝对路径或模块路径，需要更复杂的解析逻辑
    return null;
  }

  /**
   * 提取共享代码
   */
  async extractSharedCode() {
    console.log(chalk.gray('提取共享代码...'));
    
    // 确保目录存在
    const utilsDir = path.join(this.projectPath, this.options.utilsDir);
    await fs.ensureDir(utilsDir);
    
    // 生成变量文件
    await this.generateVariablesFile();
    
    // 生成混入文件
    await this.generateMixinsFile();
    
    // 生成函数文件
    await this.generateFunctionsFile();
  }

  /**
   * 生成变量文件
   */
  async generateVariablesFile() {
    const utilsDir = path.join(this.projectPath, this.options.utilsDir);
    await fs.ensureDir(utilsDir);

    const variablesPath = path.join(utilsDir, '_variables.scss');

    let content = '// 自动生成的变量文件\n';
    content += '// 包含项目中所有共享的 Sass 变量\n\n';

    // 按字母顺序排序变量
    const sortedVariables = Array.from(this.variables.keys()).sort();

    for (const varName of sortedVariables) {
      const definitions = this.variables.get(varName);

      // 选择最合适的定义（优先选择带 !default 的）
      const bestDef = definitions.find(def => def.hasDefault) || definitions[0];

      content += `$${varName}: ${bestDef.value};\n`;
    }

    await fs.writeFile(variablesPath, content, 'utf8');

    if (this.options.verbose) {
      console.log(chalk.gray(`生成变量文件: ${path.relative(this.projectPath, variablesPath)}`));
    }
  }

  /**
   * 生成混入文件
   */
  async generateMixinsFile() {
    const utilsDir = path.join(this.projectPath, this.options.utilsDir);
    await fs.ensureDir(utilsDir);

    const mixinsPath = path.join(utilsDir, '_mixins.scss');

    let content = '// 自动生成的混入文件\n';
    content += '// 包含项目中所有共享的 Sass 混入\n\n';
    content += '@use "variables" as *;\n\n';

    // 这里需要从原文件中提取完整的混入定义
    // 简化实现，仅添加注释
    content += '// TODO: 从原文件中提取混入定义\n';
    content += '// 混入列表:\n';

    for (const mixinName of this.mixins.keys()) {
      content += `// - ${mixinName}\n`;
    }

    await fs.writeFile(mixinsPath, content, 'utf8');

    if (this.options.verbose) {
      console.log(chalk.gray(`生成混入文件: ${path.relative(this.projectPath, mixinsPath)}`));
    }
  }

  /**
   * 生成函数文件
   */
  async generateFunctionsFile() {
    const utilsDir = path.join(this.projectPath, this.options.utilsDir);
    await fs.ensureDir(utilsDir);

    const functionsPath = path.join(utilsDir, '_functions.scss');

    let content = '// 自动生成的函数文件\n';
    content += '// 包含项目中所有共享的 Sass 函数\n\n';
    content += '@use "variables" as *;\n\n';

    // 简化实现，仅添加注释
    content += '// TODO: 从原文件中提取函数定义\n';
    content += '// 函数列表:\n';

    for (const functionName of this.functions.keys()) {
      content += `// - ${functionName}\n`;
    }

    await fs.writeFile(functionsPath, content, 'utf8');

    if (this.options.verbose) {
      console.log(chalk.gray(`生成函数文件: ${path.relative(this.projectPath, functionsPath)}`));
    }
  }

  /**
   * 生成桶文件
   */
  async generateBarrelFiles() {
    console.log(chalk.gray('生成桶文件...'));
    
    // 生成主桶文件
    await this.generateMainBarrelFile();
    
    // 生成组件桶文件（如果有组件目录）
    await this.generateComponentBarrelFile();
  }

  /**
   * 生成主桶文件
   */
  async generateMainBarrelFile() {
    const stylesDir = path.join(this.projectPath, this.options.stylesDir);
    await fs.ensureDir(stylesDir);

    const barrelPath = path.join(stylesDir, 'index.scss');

    let content = '// 主样式桶文件\n';
    content += '// 这是项目样式系统的统一入口\n\n';

    // 转发工具模块
    content += '// 转发工具模块\n';
    content += '@forward "utils/variables";\n';
    content += '@forward "utils/mixins";\n';
    content += '@forward "utils/functions";\n\n';

    // 可选：转发组件模块
    const componentsDir = path.join(this.projectPath, this.options.componentsDir);
    if (await fs.pathExists(componentsDir)) {
      content += '// 转发组件模块\n';
      content += '@forward "components";\n';
    }

    await fs.writeFile(barrelPath, content, 'utf8');

    console.log(chalk.green(`✅ 生成主桶文件: ${path.relative(this.projectPath, barrelPath)}`));
  }

  /**
   * 生成组件桶文件
   */
  async generateComponentBarrelFile() {
    const componentsDir = path.join(this.projectPath, this.options.componentsDir);
    
    if (!(await fs.pathExists(componentsDir))) {
      return;
    }
    
    const barrelPath = path.join(componentsDir, 'index.scss');
    
    let content = '// 组件样式桶文件\n';
    content += '// 转发所有组件样式模块\n\n';
    
    // 查找组件文件
    const componentFiles = glob.sync('**/*.scss', {
      cwd: componentsDir,
      ignore: ['index.scss']
    });
    
    for (const file of componentFiles) {
      const moduleName = path.basename(file, '.scss');
      // 使用文件名而不是完整路径
      const fileName = path.basename(file);

      content += `@forward "${fileName}" as ${moduleName}-*;\n`;
    }
    
    await fs.writeFile(barrelPath, content, 'utf8');
    
    if (this.options.verbose) {
      console.log(chalk.gray(`生成组件桶文件: ${path.relative(this.projectPath, barrelPath)}`));
    }
  }

  /**
   * 更新文件引用
   */
  async updateFileReferences() {
    console.log(chalk.gray('更新文件引用...'));
    
    // 这里需要实现更新所有文件中的 @import/@use 语句
    // 指向新的桶文件结构
    
    // 简化实现：仅输出建议
    console.log(chalk.yellow('📝 建议手动更新以下引用:'));
    console.log(chalk.gray('  - 将变量引用改为: @use "src/styles" as *;'));
    console.log(chalk.gray('  - 将混入引用改为: @use "src/styles" as *;'));
    console.log(chalk.gray('  - 移除重复的 @import 语句'));
  }
}

module.exports = SassArchitectureRefactor;
