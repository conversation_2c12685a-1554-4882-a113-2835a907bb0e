const fs = require('fs-extra')
const path = require('path')
const { execSync, spawn } = require('child_process')
const chalk = require('chalk')
const glob = require('glob')

// 引入增强功能模块
const SassPathResolver = require('./sass/pathResolver')
const SassErrorDiagnostic = require('./sass/errorDiagnostic')

/**
 * Sass 迁移器
 * 处理 Sass/SCSS 文件从 @import 到 @use 的语法迁移
 */
class SassMigrator {
	constructor (projectPath, options = {}) {
		this.projectPath = path.resolve(projectPath)
		this.options = {
			backup: true,
			verbose: false,
			dryRun: false,
			include: ['**/*.scss', '**/*.sass'],
			exclude: ['node_modules/**', 'dist/**', 'build/**'],
			useEnhancedMigration: true, // 启用增强迁移功能
			autoFix: true, // 启用自动修复
			...options
		}

		this.stats = {
			totalFiles: 0,
			processedFiles: 0,
			skippedFiles: 0,
			errorFiles: 0,
			errors: [],
			enhancedMigrations: 0, // 使用增强迁移的文件数
			autoFixes: 0 // 自动修复的问题数
		}

		// 初始化增强功能模块
		if (this.options.useEnhancedMigration) {
			this.pathResolver = new SassPathResolver(this.projectPath, options)
			this.errorDiagnostic = new SassErrorDiagnostic(this.projectPath, options)
		}
	}

	/**
	 * 初始化迁移器
	 */
	async initialize () {
		if (this.options.useEnhancedMigration) {
			await this.pathResolver.initialize()
			console.log(chalk.gray('已初始化增强迁移功能'))
		}
	}

	/**
	 * 检查 sass-migrator 是否可用
	 */
	async isSassMigratorAvailable () {
		try {
			execSync('sass-migrator --version', { stdio: 'ignore' })
			return true
		} catch (error) {
			return false
		}
	}

	/**
	 * 查找所有 Sass/SCSS 文件
	 */
	async findSassFiles () {
		const allFiles = []

		for (const pattern of this.options.include) {
			const files = glob.sync(pattern, {
				cwd: this.projectPath,
				absolute: true,
				ignore: this.options.exclude
			})
			allFiles.push(...files)
		}

		// 去重并过滤已存在的文件
		const uniqueFiles = [...new Set(allFiles)]
		const existingFiles = []

		for (const file of uniqueFiles) {
			if (await fs.pathExists(file)) {
				existingFiles.push(file)
			}
		}

		return existingFiles
	}

	/**
	 * 检查文件是否需要迁移
	 */
	async needsMigration (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')

			// 检查是否包含 @import 规则
			const importRegex = /@import\s+['"][^'"]+['"];?/g
			const hasImports = importRegex.test(content)

			// 只要包含 @import 语句就需要迁移
			return hasImports
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  无法读取文件: ${filePath}`))
			return false
		}
	}

	/**
	 * 备份文件
	 */
	async backupFile (filePath) {
		if (!this.options.backup) {
			return null
		}

		const backupPath = `${filePath}.sass-backup`
		await fs.copy(filePath, backupPath)
		return backupPath
	}

	/**
	 * 使用 sass-migrator 迁移单个文件
	 */
	async migrateFile (filePath) {
		try {
			console.log(chalk.gray(`  处理文件: ${path.relative(this.projectPath, filePath)}`))

			// 检查是否需要迁移
			if (!await this.needsMigration(filePath)) {
				console.log(chalk.gray(`    跳过: 文件不需要迁移`))
				this.stats.skippedFiles++
				return { success: true, skipped: true }
			}

			// 备份文件
			let backupPath = null
			if (this.options.backup) {
				backupPath = await this.backupFile(filePath)
				console.log(chalk.gray(`    已备份到: ${path.basename(backupPath)}`))
			}

			let useEnhancedMigration = false
			// if (this.options.useEnhancedMigration) {
			//   const needsPreprocessing = await this.needsPreprocessing(filePath);
			//   if (needsPreprocessing) {
			//     console.log(chalk.gray(`    检测到复杂路径，使用增强迁移功能`));
			//     await this.preprocessFile(filePath);
			//     useEnhancedMigration = true;
			//     this.stats.enhancedMigrations++;
			//   }
			// }

			// 构建 sass-migrator 命令
			const args = [
				'module',  // 使用 module 迁移器
				filePath
			]

			if (this.options.dryRun) {
				args.push('--dry-run')
			}

			if (this.options.verbose) {
				args.push('--verbose')
			}

			try {
				// 执行 sass-migrator
				await this.executeSassMigrator(args)
				console.log(chalk.green(`    ✅ 迁移成功`))
				this.stats.processedFiles++

				return {
					success: true,
					backupPath,
					skipped: false,
					useEnhancedMigration
				}

			} catch (sassMigratorError) {
				console.log(sassMigratorError)
				// sass-migrator 失败，尝试使用内置迁移逻辑
				if (this.options.useEnhancedMigration && this.shouldFallbackToInternalMigration(sassMigratorError)) {
					console.log(chalk.yellow(`    sass-migrator 失败，尝试内置迁移逻辑`))

					const internalResult = await this.migrateFileInternal(filePath)
					if (internalResult.success) {
						console.log(chalk.green(`    ✅ 内置迁移成功`))
						this.stats.processedFiles++
						this.stats.enhancedMigrations++

						return {
							success: true,
							backupPath,
							skipped: false,
							useEnhancedMigration: true,
							fallbackUsed: true
						}
					}
				}

				// 重新抛出原始错误
				throw sassMigratorError
			}

		} catch (error) {
			console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`))

			// 尝试诊断和自动修复错误
			if (this.options.useEnhancedMigration && this.options.autoFix) {
				const fixResult = await this.tryAutoFix(filePath, error)
				if (fixResult.fixed) {
					console.log(chalk.green(`    🔧 自动修复成功，重新尝试迁移`))
					this.stats.autoFixes++
					// 递归重新尝试迁移
					return await this.migrateFile(filePath)
				}
			}

			this.stats.errorFiles++
			this.stats.errors.push({
				file: filePath,
				error: error.message
			})

			return {
				success: false,
				error: error.message,
				skipped: false
			}
		}
	}

	/**
	 * 检查文件是否需要预处理
	 */
	async needsPreprocessing (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')

			// 检查是否包含复杂的导入路径
			const complexPatterns = [
				/~[^/]+\//, // webpack 样式的 node_modules 引用
				/@import\s+['"][^'"]*element-ui[^'"]*['"]/, // Element UI 导入
				/@import\s+['"][^'"]*bootstrap[^'"]*['"]/, // Bootstrap 导入
				/@import\s+['"][^'"]*@[^'"]*['"]/ // 其他别名导入
			]

			return complexPatterns.some(pattern => pattern.test(content))
		} catch (error) {
			return false
		}
	}

	/**
	 * 预处理文件，解决路径问题
	 */
	async preprocessFile (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')
			let processedContent = content

			// 使用路径解析器处理导入语句
			processedContent = processedContent.replace(
				/@import\s+['"]([^'"]+)['"](?:\s*;)?/g,
				(match, importPath) => {
					const resolvedPath = this.pathResolver.resolvePath(importPath, filePath)
					return `@import "${resolvedPath}";`
				}
			)

			// 只有在内容发生变化时才写入文件
			if (processedContent !== content) {
				await fs.writeFile(filePath, processedContent, 'utf8')
				if (this.options.verbose) {
					console.log(chalk.gray(`    已预处理路径`))
				}
			}
		} catch (error) {
			console.warn(chalk.yellow(`    预处理失败: ${error.message}`))
		}
	}

	/**
	 * 判断是否应该回退到内置迁移逻辑
	 */
	shouldFallbackToInternalMigration (error) {
		const fallbackPatterns = [
			/Could not find Sass file/,
			/File to import not found/,
			/Can't find stylesheet/,
			/Invalid import/
		]

		return fallbackPatterns.some(pattern => pattern.test(error.message))
	}

	/**
	 * 使用内置逻辑迁移文件
	 */
	async migrateFileInternal (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')
			let newContent = content

			// 转换 @import 为 @use
			newContent = newContent.replace(
				/@import\s+['"]([^'"]+)['"](?:\s*;)?/g,
				(match, importPath) => {
					const resolvedPath = this.pathResolver.resolvePath(importPath, filePath)
					return this.pathResolver.convertImportToUse(`@import "${resolvedPath}";`, filePath)
				}
			)

			// 写入文件
			if (!this.options.dryRun) {
				await fs.writeFile(filePath, newContent, 'utf8')
			}

			return { success: true }
		} catch (error) {
			return { success: false, error: error.message }
		}
	}

	/**
	 * 尝试自动修复错误
	 */
	async tryAutoFix (filePath, error) {
		try {
			// 使用错误诊断模块分析问题
			const diagnostics = await this.errorDiagnostic.diagnoseFile(filePath)

			let fixed = false
			for (const diagnostic of diagnostics) {
				if (diagnostic.autoFixable) {
					const fixResult = await this.errorDiagnostic.createFixSuggestion(diagnostic)
					if (fixResult && fixResult.canAutoFix) {
						await this.errorDiagnostic.applyFix(fixResult)
						fixed = true
						if (this.options.verbose) {
							console.log(chalk.gray(`    已修复: ${diagnostic.description}`))
						}
					}
				}
			}

			return { fixed }
		} catch (fixError) {
			if (this.options.verbose) {
				console.warn(chalk.yellow(`    自动修复失败: ${fixError.message}`))
			}
			return { fixed: false }
		}
	}

	/**
	 * 执行 sass-migrator 命令
	 */
	async executeSassMigrator (args) {
		return new Promise((resolve, reject) => {
			const child = spawn('sass-migrator', args, {
				cwd: this.projectPath,
				stdio: ['pipe', 'pipe', 'pipe']
			})

			let stdout = ''
			let stderr = ''

			child.stdout.on('data', (data) => {
				stdout += data.toString()
			})

			child.stderr.on('data', (data) => {
				stderr += data.toString()
			})

			child.on('close', (code) => {
				if (code === 0) {
					resolve(stdout)
				} else {
					reject(new Error(stderr || `sass-migrator exited with code ${code}`))
				}
			})

			child.on('error', (error) => {
				reject(new Error(`Failed to start sass-migrator: ${error.message}`))
			})
		})
	}

	/**
	 * 执行 Sass 迁移
	 */
	async migrate () {
		console.log(chalk.blue('🎨 开始 Sass 语法迁移...'))

		try {
			// 初始化增强功能
			await this.initialize()

			// 检查 sass-migrator 是否可用
			const sassMigratorAvailable = await this.isSassMigratorAvailable()
			if (!sassMigratorAvailable && !this.options.useEnhancedMigration) {
				throw new Error('sass-migrator 不可用，请安装: npm install -g sass-migrator')
			}

			if (!sassMigratorAvailable && this.options.useEnhancedMigration) {
				console.log(chalk.yellow('⚠️  sass-migrator 不可用，将使用内置迁移逻辑'))
			}

			// 查找所有 Sass 文件
			const sassFiles = await this.findSassFiles()
			this.stats.totalFiles = sassFiles.length

			if (sassFiles.length === 0) {
				console.log(chalk.gray('未找到需要迁移的 Sass/SCSS 文件'))
				return this.stats
			}

			console.log(chalk.blue(`找到 ${sassFiles.length} 个 Sass/SCSS 文件`))

			// 逐个处理文件
			for (const filePath of sassFiles) {
				await this.migrateFile(filePath)
			}

			// 打印统计信息
			this.printStats()

			return this.stats

		} catch (error) {
			console.error(chalk.red(`Sass 迁移失败: ${error.message}`))
			throw error
		}
	}

	/**
	 * 打印统计信息
	 */
	printStats () {
		console.log('\n' + chalk.bold('📊 Sass 迁移统计:'))
		console.log(`总文件数: ${this.stats.totalFiles}`)
		console.log(`已处理: ${this.stats.processedFiles}`)
		console.log(`已跳过: ${this.stats.skippedFiles}`)
		console.log(`错误: ${this.stats.errorFiles}`)

		if (this.options.useEnhancedMigration) {
			console.log(`增强迁移: ${this.stats.enhancedMigrations}`)
			console.log(`自动修复: ${this.stats.autoFixes}`)
		}

		if (this.stats.errors.length > 0) {
			console.log(chalk.yellow('\n⚠️  错误文件:'))
			this.stats.errors.forEach(({ file, error }) => {
				console.log(chalk.red(`  ${path.relative(this.projectPath, file)}: ${error}`))
			})

			if (this.options.useEnhancedMigration) {
				console.log(chalk.gray('\n💡 提示: 启用了增强迁移功能，可以处理复杂的路径问题'))
			}
		}

		if (this.stats.processedFiles > 0) {
			console.log(chalk.green(`\n✅ ${this.stats.processedFiles} 个文件成功迁移到 @use 语法`))

			if (this.stats.enhancedMigrations > 0) {
				console.log(chalk.blue(`🚀 其中 ${this.stats.enhancedMigrations} 个文件使用了增强迁移功能`))
			}
		}
	}

	/**
	 * 恢复备份文件
	 */
	async restoreBackups () {
		try {
			const backupFiles = glob.sync('**/*.sass-backup', {
				cwd: this.projectPath,
				absolute: true
			})

			for (const backupFile of backupFiles) {
				const originalFile = backupFile.replace('.sass-backup', '')
				await fs.move(backupFile, originalFile, { overwrite: true })
				console.log(chalk.gray(`已恢复: ${path.relative(this.projectPath, originalFile)}`))
			}

			console.log(chalk.green(`✅ 已恢复 ${backupFiles.length} 个备份文件`))
		} catch (error) {
			console.error(chalk.red(`恢复备份失败: ${error.message}`))
			throw error
		}
	}

	/**
	 * 清理备份文件
	 */
	async cleanBackups () {
		try {
			const backupFiles = glob.sync('**/*.sass-backup', {
				cwd: this.projectPath,
				absolute: true
			})

			for (const backupFile of backupFiles) {
				await fs.remove(backupFile)
			}

			if (backupFiles.length > 0) {
				console.log(chalk.gray(`🧹 已清理 ${backupFiles.length} 个备份文件`))
			}
		} catch (error) {
			console.warn(chalk.yellow(`清理备份文件时出错: ${error.message}`))
		}
	}

	/**
	 * 获取统计信息
	 */
	getStats () {
		return { ...this.stats }
	}
}

module.exports = SassMigrator
