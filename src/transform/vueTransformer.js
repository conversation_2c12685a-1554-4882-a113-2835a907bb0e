/**
 * Vue 转换器
 * 提供自定义的 Vue 2 到 Vue 3 代码转换规则
 */
class VueTransformer {
  /**
   * 应用自定义 Vue 转换规则
   * @param {string} code - 源代码
   * @returns {string} 转换后的代码
   */
  transform(code) {
    // 统一处理 gogocodeTransfer 导入 - 将所有相对路径替换为全局路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理其他可能的 gogocodeTransfer 导入格式
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理更复杂的相对路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 替换 Element UI 导入
    code = code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['\"]/g,
      'import { $1 } from \'element-plus\''
    )

    // 替换 Element UI 完整导入
    code = code.replace(
      /import\s+ElementUI\s+from\s+['"]element-ui['\"]/g,
      'import ElementPlus from \'element-plus\''
    )

    // 替换 Element UI CSS 导入
    code = code.replace(
      /import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['\"]/g,
      'import \'element-plus/dist/index.css\''
    )

    // 替换 Vue 导入 - 关键转换
    code = code.replace(
      /import\s+Vue\s+from\s+['"]vue['\"]/g,
      'import { createApp } from \'vue\''
    )

    // 替换 Vue 2 的全局 API
    code = code.replace(/Vue\.extend\(/g, 'defineComponent(')
    code = code.replace(/Vue\.component\(/g, 'app.component(')
    code = code.replace(/Vue\.use\(/g, 'app.use(')
    code = code.replace(/Vue\.config\./g, 'app.config.')
    code = code.replace(/Vue\.mixin\(/g, 'app.mixin(')
    code = code.replace(/Vue\.directive\(/g, 'app.directive(')
    code = code.replace(/Vue\.filter\(/g, 'app.config.globalProperties.$filters = app.config.globalProperties.$filters || {}; app.config.globalProperties.$filters[')

    // 替换 Element UI 使用
    code = code.replace(/Vue\.use\(ElementUI\)/g, 'app.use(ElementPlus)')

    // 替换 new Vue() 为 createApp() - 更复杂的处理
    // 首先处理简单的 new Vue({...}).$mount('#app') 模式
    code = code.replace(
      /new\s+Vue\(\s*{([^}]+)}\s*\)\.\$mount\(['"]#app['"]\)/g,
      'const app = createApp({\n$1\n})\napp.mount(\'#app\')'
    )

    // 处理更复杂的 new Vue() 模式
    code = code.replace(
      /new\s+Vue\(\s*{([\s\S]*?)}\s*\)\.\$mount\(['"]#app['"]\)/g,
      'const app = createApp({\n$1\n})\napp.mount(\'#app\')'
    )

    // 处理没有 $mount 的 new Vue() 模式
    code = code.replace(
      /new\s+Vue\(\s*{([\s\S]*?)}\s*\)/g,
      'const app = createApp({\n$1\n})'
    )

    // 特殊处理 main.js 中的 Vue 实例创建
    // 匹配 new Vue({ router, store, render: h => h(App) }).$mount('#app')
    code = code.replace(
      /new\s+Vue\(\s*{\s*router,\s*store,\s*render:\s*h\s*=>\s*h\(App\)\s*}\s*\)\.\$mount\(['"]#app['"]\)/g,
      'const app = createApp({\n  router,\n  store,\n  render: h => h(App)\n})\napp.mount(\'#app\')'
    )

    // 处理 Vue.config.productionTip
    code = code.replace(
      /Vue\.config\.productionTip\s*=\s*false/g,
      '// Vue 3 中移除了 productionTip 配置'
    )

    // 处理 Vue.mixin
    code = code.replace(
      /Vue\.mixin\(\s*{([\s\S]*?)}\s*\)/g,
      'app.mixin({\n$1\n})'
    )

    // 处理 Vue.directive
    code = code.replace(
      /Vue\.directive\(\s*['"]([^'"]+)['"],\s*{([\s\S]*?)}\s*\)/g,
      'app.directive(\'$1\', {\n$2\n})'
    )

    // 处理 Vue.filter - 转换为全局属性
    code = code.replace(
      /Vue\.filter\(\s*['"]([^'"]+)['"],\s*function\s*\(([^)]*)\)\s*{([\s\S]*?)}\s*\)/g,
      'app.config.globalProperties.$filters = app.config.globalProperties.$filters || {}\napp.config.globalProperties.$filters[\'$1\'] = function($2) {\n$3\n}'
    )

    // 替换 $refs 访问方式（需要更复杂的 AST 处理）
    // 这里只做简单的字符串替换示例

    return code
  }
}

module.exports = VueTransformer
