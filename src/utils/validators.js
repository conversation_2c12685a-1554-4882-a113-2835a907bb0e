const fs = require('fs-extra');
const path = require('path');
const { MigrationError, ErrorCodes } = require('./error-handler');

/**
 * 项目验证器
 */
class ProjectValidator {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
  }

  /**
   * 验证项目基本结构
   */
  async validateProject() {
    const validations = [
      this.validateProjectExists(),
      this.validatePackageJson(),
      this.validateVueProject(),
      this.validateVueVersion()
    ];

    const results = await Promise.allSettled(validations);
    const errors = [];

    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        errors.push(result.reason);
      }
    });

    if (errors.length > 0) {
      throw errors[0]; // 返回第一个错误
    }

    return {
      valid: true,
      projectPath: this.projectPath,
      type: 'vue'
    };
  }

  /**
   * 验证项目目录是否存在
   */
  async validateProjectExists() {
    if (!await fs.pathExists(this.projectPath)) {
      throw MigrationError.projectNotFound(this.projectPath);
    }
    return true;
  }

  /**
   * 验证 package.json 文件
   */
  async validatePackageJson() {
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    
    if (!await fs.pathExists(packageJsonPath)) {
      throw new MigrationError(
        '未找到 package.json 文件',
        ErrorCodes.FILE_NOT_FOUND,
        'validation',
        { file: packageJsonPath }
      );
    }

    try {
      const packageJson = await fs.readJson(packageJsonPath);
      
      if (!packageJson.name) {
        throw new MigrationError(
          'package.json 缺少 name 字段',
          ErrorCodes.VALIDATION_FAILED,
          'validation',
          { file: packageJsonPath }
        );
      }

      return packageJson;
    } catch (error) {
      if (error instanceof MigrationError) {
        throw error;
      }
      throw new MigrationError(
        '无法解析 package.json 文件',
        ErrorCodes.FILE_READ_FAILED,
        'validation',
        { file: packageJsonPath, originalError: error.message }
      );
    }
  }

  /**
   * 验证是否为 Vue 项目
   */
  async validateVueProject() {
    const packageJson = await this.validatePackageJson();
    const dependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    if (!dependencies.vue) {
      throw MigrationError.projectNotVue(this.projectPath);
    }

    return true;
  }

  /**
   * 验证 Vue 版本
   */
  async validateVueVersion() {
    const packageJson = await this.validatePackageJson();
    const dependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    const vueVersion = dependencies.vue;
    
    if (vueVersion) {
      // 检查是否已经是 Vue 3
      if (vueVersion.startsWith('3.')) {
        return {
          version: vueVersion,
          isVue3: true,
          warning: '检测到 Vue 3 项目，可能不需要迁移'
        };
      } else if (vueVersion.startsWith('2.')) {
        return {
          version: vueVersion,
          isVue3: false,
          canMigrate: true
        };
      } else {
        return {
          version: vueVersion,
          isVue3: false,
          canMigrate: true,
          warning: '检测到非标准 Vue 版本，迁移可能不完全'
        };
      }
    }

    return {
      version: 'unknown',
      isVue3: false,
      canMigrate: false,
      error: '无法确定 Vue 版本'
    };
  }

  /**
   * 验证项目结构
   */
  async validateProjectStructure() {
    const structure = {
      src: await fs.pathExists(path.join(this.projectPath, 'src')),
      public: await fs.pathExists(path.join(this.projectPath, 'public')),
      node_modules: await fs.pathExists(path.join(this.projectPath, 'node_modules')),
      package_lock: await fs.pathExists(path.join(this.projectPath, 'package-lock.json')),
      yarn_lock: await fs.pathExists(path.join(this.projectPath, 'yarn.lock')),
      pnpm_lock: await fs.pathExists(path.join(this.projectPath, 'pnpm-lock.yaml'))
    };

    return structure;
  }

  /**
   * 验证源码目录
   */
  async validateSourceDirectory() {
    const srcPath = path.join(this.projectPath, 'src');
    
    if (!await fs.pathExists(srcPath)) {
      throw new MigrationError(
        '未找到 src 目录',
        ErrorCodes.VALIDATION_FAILED,
        'validation',
        { path: srcPath }
      );
    }

    const srcStats = await fs.stat(srcPath);
    if (!srcStats.isDirectory()) {
      throw new MigrationError(
        'src 不是一个目录',
        ErrorCodes.VALIDATION_FAILED,
        'validation',
        { path: srcPath }
      );
    }

    return srcPath;
  }

  /**
   * 验证文件权限
   */
  async validateFilePermissions() {
    const testFile = path.join(this.projectPath, '.migration-test');
    
    try {
      // 测试写入权限
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);
      return true;
    } catch (error) {
      throw new MigrationError(
        '没有项目目录的写入权限',
        ErrorCodes.PERMISSION_DENIED,
        'validation',
        { path: this.projectPath, originalError: error.message }
      );
    }
  }
}

/**
 * 配置验证器
 */
class ConfigValidator {
  constructor(config) {
    this.config = config;
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = [];

    // 验证必需字段
    if (!this.config.version) {
      errors.push('缺少版本信息');
    }

    if (!this.config.steps) {
      errors.push('缺少步骤配置');
    }

    if (!this.config.strategies) {
      errors.push('缺少策略配置');
    }

    // 验证步骤配置
    if (this.config.steps) {
      const stepErrors = this.validateSteps(this.config.steps);
      errors.push(...stepErrors);
    }

    // 验证策略配置
    if (this.config.strategies) {
      const strategyErrors = this.validateStrategies(this.config.strategies);
      errors.push(...strategyErrors);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证步骤配置
   */
  validateSteps(steps) {
    const errors = [];

    Object.entries(steps).forEach(([name, step]) => {
      if (!step.name) {
        errors.push(`步骤 ${name} 缺少 name 字段`);
      }

      if (typeof step.enabled !== 'boolean') {
        errors.push(`步骤 ${name} 缺少 enabled 字段`);
      }

      if (typeof step.order !== 'number') {
        errors.push(`步骤 ${name} 缺少 order 字段`);
      }
    });

    return errors;
  }

  /**
   * 验证策略配置
   */
  validateStrategies(strategies) {
    const errors = [];

    Object.entries(strategies).forEach(([name, strategy]) => {
      if (!strategy.name) {
        errors.push(`策略 ${name} 缺少 name 字段`);
      }

      if (typeof strategy.priority !== 'number') {
        errors.push(`策略 ${name} 缺少 priority 字段`);
      }

      if (!Array.isArray(strategy.requirements)) {
        errors.push(`策略 ${name} 缺少 requirements 字段`);
      }
    });

    return errors;
  }
}

/**
 * 选项验证器
 */
class OptionsValidator {
  constructor(options) {
    this.options = options;
  }

  /**
   * 验证迁移选项
   */
  validate() {
    const errors = [];

    // 验证项目路径
    if (!this.options.projectPath) {
      errors.push('缺少项目路径');
    }

    // 验证 AI 相关选项
    if (this.options.aiApiKey && typeof this.options.aiApiKey !== 'string') {
      errors.push('AI API Key 必须是字符串');
    }

    // 验证构建命令
    if (this.options.buildCommand && typeof this.options.buildCommand !== 'string') {
      errors.push('构建命令必须是字符串');
    }

    // 验证布尔选项
    const booleanOptions = [
      'skipDependencyCheck',
      'skipAIRepair',
      'skipESLint',
      'skipBuild',
      'dryRun',
      'verbose'
    ];

    booleanOptions.forEach(option => {
      if (this.options[option] !== undefined && typeof this.options[option] !== 'boolean') {
        errors.push(`${option} 必须是布尔值`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 标准化选项
   */
  normalize() {
    return {
      projectPath: this.options.projectPath,
      skipDependencyCheck: this.options.skipDependencyCheck || false,
      skipAIRepair: this.options.skipAIRepair || false,
      skipESLint: this.options.skipESLint || false,
      skipBuild: this.options.skipBuild || false,
      aiApiKey: this.options.aiApiKey || process.env.OPENAI_API_KEY,
      buildCommand: this.options.buildCommand || 'npm run build',
      dryRun: this.options.dryRun || false,
      verbose: this.options.verbose || false
    };
  }
}

module.exports = {
  ProjectValidator,
  ConfigValidator,
  OptionsValidator
}; 