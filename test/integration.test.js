const BuildFixer = require('../src/buildFixer');
const AIRepairer = require('../src/aiRepairer');
const { AIService } = require('../src/ai/ai-service');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

describe('Integration Tests', () => {
  let buildFixer;
  let aiRepairer;

  beforeEach(() => {
    aiRepairer = new AIRepairer();
    buildFixer = new BuildFixer('/test/path', {
      buildCommand: 'echo "test"',
      aiRepairer: aiRepairer
    });
  });

  it('should integrate BuildFixer with AIRepairer', () => {
    expect(buildFixer.options.aiRepairer).toBe(aiRepairer);
  });

  it('should ensure BuildFixer inherits from AIService', () => {
    expect(buildFixer).toBeInstanceOf(AIService);
  });
});
