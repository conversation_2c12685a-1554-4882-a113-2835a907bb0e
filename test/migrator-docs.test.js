const { getMigratorDocPath } = require('../src/migrator/migrator-docs');
const path = require('path');

describe('getMigratorDocPath', () => {
  test('should return correct path for regular package names', () => {
    const docPath = getMigratorDocPath('vuedraggable');
    expect(docPath).toBe(path.resolve(__dirname, '../src/migrator/docs/vuedraggable.md'));
  });

  test('should return correct path for scoped package names', () => {
    const docPath = getMigratorDocPath('@riophae/vue-treeselect');
    expect(docPath).toBe(path.resolve(__dirname, '../src/migrator/docs/riophae-vue-treeselect.md'));
  });

  test('should handle @tinymce/tinymce-vue correctly', () => {
    const docPath = getMigratorDocPath('@tinymce/tinymce-vue');
    expect(docPath).toBe(path.resolve(__dirname, '../src/migrator/docs/tinymce-tinymce-vue.md'));
  });

  test('should handle @wangeditor/editor-for-vue correctly', () => {
    const docPath = getMigratorDocPath('@wangeditor/editor-for-vue');
    expect(docPath).toBe(path.resolve(__dirname, '../src/migrator/docs/wangeditor-editor-for-vue.md'));
  });

  test('should return null for non-existent package documentation', () => {
    const docPath = getMigratorDocPath('non-existent-package');
    expect(docPath).toBeNull();
  });

  test('should return null for non-existent scoped package documentation', () => {
    const docPath = getMigratorDocPath('@scope/non-existent-package');
    expect(docPath).toBeNull();
  });
}); 