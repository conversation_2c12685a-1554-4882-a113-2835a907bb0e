const path = require('path');
const fs = require('fs-extra');
const SassArchitectureRefactor = require('../../src/sass/architectureRefactor');

describe('SassArchitectureRefactor', () => {
  let tempDir;
  let refactor;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('arch-refactor-');
    refactor = new SassArchitectureRefactor(tempDir, { verbose: false });
  });

  describe('初始化', () => {
    test('应该正确初始化架构重构器', () => {
      expect(refactor.projectPath).toBe(tempDir);
      expect(refactor.dependencyGraph).toBeInstanceOf(Map);
      expect(refactor.variables).toBeInstanceOf(Map);
      expect(refactor.mixins).toBeInstanceOf(Map);
      expect(refactor.functions).toBeInstanceOf(Map);
    });
  });

  describe('文件分析', () => {
    test('应该找到所有 Sass 文件', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const files = await refactor.findSassFiles();
      expect(files.length).toBeGreaterThan(0);
      expect(files.some(file => file.includes('variables.scss'))).toBe(true);
      expect(files.some(file => file.includes('mixins.scss'))).toBe(true);
    });

    test('应该排除指定的目录', async () => {
      await global.testUtils.createTestProject(tempDir);
      await fs.ensureDir(path.join(tempDir, 'node_modules/some-package'));
      await fs.writeFile(path.join(tempDir, 'node_modules/some-package/style.scss'), '$color: red;');
      
      const files = await refactor.findSassFiles();
      expect(files.some(file => file.includes('node_modules'))).toBe(false);
    });
  });

  describe('依赖关系提取', () => {
    test('应该提取 @import 依赖', () => {
      const content = `
        @import "variables";
        @import "./mixins";
        @import "~bootstrap/scss/bootstrap";
      `;
      
      const dependencies = refactor.extractDependencies(content);
      expect(dependencies).toContain('variables');
      expect(dependencies).toContain('./mixins');
      expect(dependencies).toContain('~bootstrap/scss/bootstrap');
    });

    test('应该提取 @use 依赖', () => {
      const content = `
        @use "variables" as *;
        @use "./mixins" as mix;
        @use "sass:math";
      `;
      
      const dependencies = refactor.extractDependencies(content);
      expect(dependencies).toContain('variables');
      expect(dependencies).toContain('./mixins');
      expect(dependencies).toContain('sass:math');
    });
  });

  describe('变量提取', () => {
    test('应该提取变量定义', () => {
      const content = `
        $primary-color: #409eff;
        $font-size-base: 14px !default;
        $border-radius: 4px;
      `;
      
      refactor.extractVariables(content, 'test.scss');
      
      expect(refactor.variables.has('primary-color')).toBe(true);
      expect(refactor.variables.has('font-size-base')).toBe(true);
      expect(refactor.variables.has('border-radius')).toBe(true);
      
      const fontSizeVar = refactor.variables.get('font-size-base')[0];
      expect(fontSizeVar.hasDefault).toBe(true);
    });

    test('应该处理复杂的变量值', () => {
      const content = `
        $colors: (
          primary: #409eff,
          success: #67c23a
        );
        $calculated: $base-size * 1.5;
      `;
      
      refactor.extractVariables(content, 'test.scss');
      
      expect(refactor.variables.has('colors')).toBe(true);
      expect(refactor.variables.has('calculated')).toBe(true);
    });
  });

  describe('混入提取', () => {
    test('应该提取混入定义', () => {
      const content = `
        @mixin button-style {
          padding: 8px 16px;
          border-radius: 4px;
        }
        
        @mixin flex-center($direction: row) {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: $direction;
        }
      `;
      
      refactor.extractMixins(content, 'test.scss');
      
      expect(refactor.mixins.has('button-style')).toBe(true);
      expect(refactor.mixins.has('flex-center')).toBe(true);
    });
  });

  describe('函数提取', () => {
    test('应该提取函数定义', () => {
      const content = `
        @function px-to-rem($px) {
          @return $px / 16px * 1rem;
        }
        
        @function color-mix($color1, $color2, $weight: 50%) {
          @return mix($color1, $color2, $weight);
        }
      `;
      
      refactor.extractFunctions(content, 'test.scss');
      
      expect(refactor.functions.has('px-to-rem')).toBe(true);
      expect(refactor.functions.has('color-mix')).toBe(true);
    });
  });

  describe('循环依赖检测', () => {
    test('应该检测简单的循环依赖', async () => {
      await global.testUtils.createCircularDependencyProject(tempDir);
      
      await refactor.analyzeCurrentStructure();
      await refactor.detectCircularDependencies();
      
      expect(refactor.circularDependencies.length).toBeGreaterThan(0);
    });

    test('应该检测自引用', async () => {
      const selfRefContent = `
        @import "./self-reference";
        $self-color: yellow;
      `;
      
      await fs.ensureDir(path.join(tempDir, 'src/styles'));
      await fs.writeFile(path.join(tempDir, 'src/styles/self-reference.scss'), selfRefContent);
      
      await refactor.analyzeCurrentStructure();
      await refactor.detectCircularDependencies();
      
      expect(refactor.circularDependencies.length).toBeGreaterThan(0);
    });

    test('应该处理无循环依赖的项目', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      await refactor.analyzeCurrentStructure();
      await refactor.detectCircularDependencies();
      
      expect(refactor.circularDependencies.length).toBe(0);
    });
  });

  describe('变量文件生成', () => {
    test('应该生成变量文件', async () => {
      // 模拟一些变量
      refactor.variables.set('primary-color', [{ file: 'test.scss', value: '#409eff', hasDefault: false }]);
      refactor.variables.set('font-size-base', [{ file: 'test.scss', value: '14px !default', hasDefault: true }]);
      
      await refactor.generateVariablesFile();
      
      const variablesPath = path.join(tempDir, refactor.options.utilsDir, '_variables.scss');
      expect(await fs.pathExists(variablesPath)).toBe(true);
      
      const content = await fs.readFile(variablesPath, 'utf8');
      expect(content).toContain('$primary-color: #409eff;');
      expect(content).toContain('$font-size-base: 14px !default;');
    });

    test('应该按字母顺序排序变量', async () => {
      refactor.variables.set('z-index', [{ file: 'test.scss', value: '1000', hasDefault: false }]);
      refactor.variables.set('border-radius', [{ file: 'test.scss', value: '4px', hasDefault: false }]);
      refactor.variables.set('margin', [{ file: 'test.scss', value: '8px', hasDefault: false }]);
      
      await refactor.generateVariablesFile();
      
      const variablesPath = path.join(tempDir, refactor.options.utilsDir, '_variables.scss');
      const content = await fs.readFile(variablesPath, 'utf8');
      
      const borderIndex = content.indexOf('$border-radius');
      const marginIndex = content.indexOf('$margin');
      const zIndexIndex = content.indexOf('$z-index');
      
      expect(borderIndex).toBeLessThan(marginIndex);
      expect(marginIndex).toBeLessThan(zIndexIndex);
    });
  });

  describe('桶文件生成', () => {
    test('应该生成主桶文件', async () => {
      await refactor.generateMainBarrelFile();
      
      const barrelPath = path.join(tempDir, refactor.options.stylesDir, 'index.scss');
      expect(await fs.pathExists(barrelPath)).toBe(true);
      
      const content = await fs.readFile(barrelPath, 'utf8');
      expect(content).toContain('@forward "utils/variables";');
      expect(content).toContain('@forward "utils/mixins";');
      expect(content).toContain('@forward "utils/functions";');
    });

    test('应该在有组件目录时生成组件桶文件', async () => {
      const componentsDir = path.join(tempDir, refactor.options.componentsDir);
      await fs.ensureDir(componentsDir);
      await fs.writeFile(path.join(componentsDir, 'button.scss'), '.button { color: red; }');
      await fs.writeFile(path.join(componentsDir, 'card.scss'), '.card { background: white; }');
      
      await refactor.generateComponentBarrelFile();
      
      const barrelPath = path.join(componentsDir, 'index.scss');
      expect(await fs.pathExists(barrelPath)).toBe(true);
      
      const content = await fs.readFile(barrelPath, 'utf8');
      expect(content).toContain('@forward "button.scss" as button-*;');
      expect(content).toContain('@forward "card.scss" as card-*;');
    });
  });

  describe('完整重构流程', () => {
    test('应该执行完整的重构流程', async () => {
      await global.testUtils.createTestProject(tempDir);
      
      const result = await refactor.refactor();
      
      expect(result).toHaveProperty('circularDependencies');
      expect(result).toHaveProperty('variablesCount');
      expect(result).toHaveProperty('mixinsCount');
      expect(result).toHaveProperty('functionsCount');
      
      // 检查生成的文件
      const utilsDir = path.join(tempDir, refactor.options.utilsDir);
      expect(await fs.pathExists(path.join(utilsDir, '_variables.scss'))).toBe(true);
      expect(await fs.pathExists(path.join(utilsDir, '_mixins.scss'))).toBe(true);
      expect(await fs.pathExists(path.join(utilsDir, '_functions.scss'))).toBe(true);
      
      const barrelPath = path.join(tempDir, refactor.options.stylesDir, 'index.scss');
      expect(await fs.pathExists(barrelPath)).toBe(true);
    });

    test('应该处理空项目', async () => {
      const result = await refactor.refactor();
      
      expect(result.variablesCount).toBe(0);
      expect(result.mixinsCount).toBe(0);
      expect(result.functionsCount).toBe(0);
      expect(result.circularDependencies).toEqual([]);
    });
  });

  describe('依赖路径解析', () => {
    test('应该解析相对路径依赖', () => {
      const dependency = './variables';
      const currentFile = 'src/styles/main.scss';

      const result = refactor.resolveDependencyPath(dependency, currentFile);
      expect(result).toBe('src/styles/variables');
    });

    test('应该解析父目录路径依赖', () => {
      const dependency = '../utils/helpers';
      const currentFile = 'src/styles/components/button.scss';

      const result = refactor.resolveDependencyPath(dependency, currentFile);
      expect(result).toBe('src/styles/utils/helpers');
    });

    test('应该处理绝对路径和模块路径', () => {
      const dependency = 'bootstrap/scss/bootstrap';
      const currentFile = 'src/styles/main.scss';
      
      const result = refactor.resolveDependencyPath(dependency, currentFile);
      expect(result).toBeNull();
    });
  });

  describe('错误处理', () => {
    test('应该处理文件读取错误', async () => {
      const nonExistentFile = path.join(tempDir, 'non-existent.scss');
      
      await expect(refactor.analyzeFile(nonExistentFile)).resolves.not.toThrow();
    });

    test('应该处理无效的 Sass 语法', async () => {
      const invalidContent = `
        $invalid-syntax: ;
        @mixin broken {
          invalid-property: value
      `;
      
      const testFile = path.join(tempDir, 'invalid.scss');
      await fs.writeFile(testFile, invalidContent);
      
      await expect(refactor.analyzeFile(testFile)).resolves.not.toThrow();
    });
  });
});
